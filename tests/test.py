import asyncio
import sys
import json
from pathlib import Path
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import FormFillingApp

async def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    app = FormFillingApp()
    
    try:
        # 启动应用
        if not await app.start():
            print("❌ 启动失败")
            return False
        
        print("✅ Agent启动成功")
        
        # 测试意图识别
        test_inputs = [
            "我要创建一个报销申请",
            "帮我填写费用报销单，我叫张三，工号是001，报销金额500元",
            "我需要请假",
        ]
        
        for i, user_input in enumerate(test_inputs, 1):
            print(f"\n--- 测试 {i}: {user_input} ---")
            
            try:
                response = await app.run_single_command(user_input)
                
                if response["success"]:
                    print(f"✅ 回复: {response['response']}")
                    print(f"📊 会话状态: {json.dumps(response['session_summary'], indent=2, ensure_ascii=False)}")
                else:
                    print(f"❌ 错误: {response['error']}")
            
            except Exception as e:
                print(f"❌ 异常: {str(e)}")
        
        print("✅ 基本功能测试完成")
        return True
    
    finally:
        await app.stop()

async def test_ocr_functionality():
    """测试OCR功能"""
    print("\n=== 测试OCR功能 ===")
    
    from src.services import OCRService
    from src.config import AgentConfig
    
    config = AgentConfig()
    ocr = OCRService(config)
    
    print(f"🔧 当前OCR提供商: {config.ocr.provider}")
    
    # 创建测试图像文件路径
    test_image_path = "test_receipt.jpg"
    
    if not Path(test_image_path).exists():
        print(f"⚠️  测试图像文件不存在: {test_image_path}")
        print("请在项目根目录放置一个名为 test_receipt.jpg 的测试票据图像")
        return False
    
    try:
        # 测试文本提取
        text = await ocr.extract_text_from_image(test_image_path)
        print(f"📄 提取的文本 ({len(text)} 字符):")
        print(text[:200] + "..." if len(text) > 200 else text)
        
        # 测试结构化数据提取
        receipt_data = await ocr.extract_structured_data(test_image_path, "receipt")
        print(f"💰 提取的报销数据:")
        print(json.dumps(receipt_data, indent=2, ensure_ascii=False))
        
        print("✅ OCR功能测试完成")
        return True
    
    except Exception as e:
        print(f"❌ OCR测试失败: {str(e)}")
        return False

async def test_chrome_mcp_connection():
    """测试Chrome MCP连接"""
    print("\n=== 测试Chrome MCP连接 ===")
    
    from src.config import AgentConfig
    from src.clients import ChromeMCPClient
    
    config = AgentConfig()
    client = ChromeMCPClient(config.chrome_mcp)
    
    try:
        # 尝试连接
        if await client.connect():
            print("✅ Chrome MCP连接成功")
            
            # 测试导航
            await client.navigate_to("https://www.example.com")
            print("✅ 页面导航成功")
            
            # 获取当前URL
            current_url = await client.get_current_url()
            print(f"📍 当前URL: {current_url}")
            
            # 截图测试
            screenshot_result = await client.take_screenshot()
            if screenshot_result.get("result"):
                print("✅ 截图功能正常")
            
            await client.disconnect()
            print("✅ Chrome MCP测试完成")
            return True
        
        else:
            print("❌ Chrome MCP连接失败")
            print("请确保Chrome MCP Server正在运行:")
            return False
    
    except Exception as e:
        print(f"❌ Chrome MCP测试失败: {str(e)}")
        return False

async def test_siliconflow_api():
    """测试硅基流动API"""
    print("\n=== 测试硅基流动API ===")
    
    from src.config import AgentConfig
    from src.clients import SiliconFlowClient
    
    config = AgentConfig()
    
    if not config.siliconflow.api_key:
        print("❌ 未配置硅基流动API密钥")
        print("请在.env文件中设置SILICONFLOW_API_KEY")
        return False
    
    try:
        async with SiliconFlowClient(config.siliconflow) as client:
            # 测试聊天完成
            messages = [
                {"role": "system", "content": "你是一个有帮助的助手。"},
                {"role": "user", "content": "你好！"}
            ]
            
            response = await client.chat_completion(messages)
            print(f"💬 API回复: {response}")
            
            # 测试意图分析
            intent_result = await client.analyze_intent("我要创建一个报销申请")
            print(f"🎯 意图分析: {json.dumps(intent_result, indent=2, ensure_ascii=False)}")
            
            print("✅ 硅基流动API测试完成")
            return True
    
    except Exception as e:
        print(f"❌ 硅基流动API测试失败: {str(e)}")
        return False

async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行测试套件\n")
    
    tests = [
        ("硅基流动API", test_siliconflow_api),
        ("Chrome MCP连接", test_chrome_mcp_connection),
        ("OCR功能", test_ocr_functionality),
        ("基本功能", test_basic_functionality),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            results[test_name] = False
    
    # 输出测试结果摘要
    print("\n" + "="*50)
    print("📊 测试结果摘要")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print("-"*50)
    print(f"总计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置")
        return False

async def main():
    """主函数"""
    if len(sys.argv) > 1:
        test_name = sys.argv[1].lower()
        
        if test_name == "basic":
            await test_basic_functionality()
        elif test_name == "ocr":
            await test_ocr_functionality()
        elif test_name == "chrome":
            await test_chrome_mcp_connection()
        elif test_name == "api":
            await test_siliconflow_api()
        else:
            await run_all_tests()
    else:
        print("可用的测试:")
        print("  python test_agent.py basic   - 测试基本功能")
        print("  python test_agent.py ocr     - 测试OCR功能")
        print("  python test_agent.py chrome  - 测试Chrome MCP")
        print("  python test_agent.py api     - 测试硅基流动API")
        print("  python test_agent.py         - 运行所有测试")

if __name__ == "__main__":
    # Windows兼容性
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())