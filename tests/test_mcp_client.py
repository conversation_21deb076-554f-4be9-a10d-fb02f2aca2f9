#!/usr/bin/env python3
"""
MCP客户端测试
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config import AgentConfig
from src.clients import ChromeMCPClient

async def test_mcp_client_basic():
    """测试MCP客户端基本功能"""
    print("=== MCP客户端基本功能测试 ===\n")
    
    config = AgentConfig()
    client = ChromeMCPClient(config.chrome_mcp)
    
    print(f"🔧 MCP服务器地址: {config.chrome_mcp.base_url}")
    print(f"🔧 连接超时: {config.chrome_mcp.timeout}秒")
    print(f"🔧 最大重试次数: {config.chrome_mcp.max_retries}")
    
    try:
        # 测试连接状态查询
        status = await client.get_connection_status()
        print(f"📊 连接状态: {status}")
        
        # 尝试连接（这可能会失败，因为MCP服务器可能没有运行）
        print("\n🔌 尝试连接到MCP服务器...")
        connected = await client.connect()
        
        if connected:
            print("✅ 连接成功！")
            
            # 测试健康检查
            health = await client.health_check()
            print(f"💚 健康检查: {'通过' if health else '失败'}")
            
            # 测试工具列表
            try:
                tools = await client.list_available_tools()
                print(f"🛠️  可用工具数量: {len(tools)}")
                for tool in tools[:3]:  # 只显示前3个工具
                    print(f"   - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
            except Exception as e:
                print(f"⚠️  获取工具列表时出错: {e}")
            
            # 断开连接
            await client.disconnect()
            print("🔌 已断开连接")
            
        else:
            print("❌ 连接失败")
            print("💡 请确保Chrome MCP服务器正在运行:")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ MCP客户端基本功能测试完成")

async def test_mcp_client_compatibility():
    """测试MCP客户端兼容性"""
    print("\n=== MCP客户端兼容性测试 ===\n")
    
    config = AgentConfig()
    client = ChromeMCPClient(config.chrome_mcp)
    
    # 测试所有方法是否存在
    methods_to_test = [
        'connect', 'disconnect', 'navigate_to', 'wait_for_load',
        'find_element', 'find_elements', 'get_element_text',
        'click_element', 'input_text', 'select_option',
        'upload_file', 'take_screenshot', 'get_page_source',
        'execute_script', 'get_current_url', 'wait_for_element',
        'is_element_visible', 'get_connection_status', 'health_check'
    ]
    
    print("🔍 检查方法兼容性:")
    for method_name in methods_to_test:
        if hasattr(client, method_name):
            method = getattr(client, method_name)
            if callable(method):
                print(f"   ✅ {method_name}")
            else:
                print(f"   ❌ {method_name} (不可调用)")
        else:
            print(f"   ❌ {method_name} (不存在)")
    
    print("\n✅ 兼容性测试完成")

async def main():
    """主函数"""
    await test_mcp_client_basic()
    await test_mcp_client_compatibility()
    
    print("\n🎉 所有测试完成！")

if __name__ == "__main__":
    # Windows兼容性
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())