#!/usr/bin/env python3
"""
智谱AI OCR测试脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config import AgentConfig
from src.clients import ZhipuOCRClient
import json

async def test_zhipu_ocr():
    """测试智谱AI OCR功能"""
    print("=== 智谱AI GLM-4.1V-Thinking OCR 测试 ===\n")
    
    # 检查配置
    config = AgentConfig()
    
    if not config.zhipu.api_key:
        print("❌ 未配置智谱AI API密钥")
        print("请在.env文件中设置 ZHIPU_API_KEY")
        return False
    
    print(f"✅ API密钥已配置: {config.zhipu.api_key[:10]}...")
    print(f"🔧 模型: {config.zhipu.model}")
    print(f"🌐 API地址: {config.zhipu.base_url}")
    
    # 检查测试图像
    test_image_path = "test_receipt.jpg"
    
    if not Path(test_image_path).exists():
        print(f"\n⚠️  测试图像文件不存在: {test_image_path}")
        print("请在项目根目录放置一个名为 test_receipt.jpg 的测试图像")
        
        # 提供一些示例图像建议
        print("\n💡 建议测试图像类型:")
        print("- 发票或收据")
        print("- 身份证")
        print("- 包含中英文的文档")
        return False
    
    print(f"✅ 找到测试图像: {test_image_path}")
    
    try:
        # 创建智谱AI OCR客户端
        client = ZhipuOCRClient(config.zhipu)
        
        async with client:
            print("\n--- 测试1: 文本提取 ---")
            
            # 测试文本提取
            text = await client.extract_text_from_image(test_image_path)
            print(f"📄 提取的文本内容 ({len(text)} 字符):")
            print("-" * 50)
            print(text)
            print("-" * 50)
            
            print("\n--- 测试2: 结构化数据提取（发票/收据） ---")
            
            # 测试结构化数据提取
            receipt_data = await client.extract_structured_data(test_image_path, "receipt")
            print("💰 提取的发票/收据数据:")
            print(json.dumps(receipt_data, indent=2, ensure_ascii=False))
            
            print("\n--- 测试3: 结构化数据提取（身份证） ---")
            
            # 测试身份证数据提取
            id_data = await client.extract_structured_data(test_image_path, "id_card")
            print("🆔 提取的身份证数据:")
            print(json.dumps(id_data, indent=2, ensure_ascii=False))
        
        print("\n✅ 智谱AI OCR测试完成！")
        return True
    
    except Exception as e:
        print(f"\n❌ 智谱AI OCR测试失败: {str(e)}")
        
        # 提供一些常见错误的解决建议
        if "401" in str(e) or "authentication" in str(e).lower():
            print("\n💡 可能的解决方案:")
            print("1. 检查API密钥是否正确")
            print("2. 确认API密钥是否有效且未过期")
            print("3. 检查账户余额是否充足")
        elif "network" in str(e).lower() or "connection" in str(e).lower():
            print("\n💡 可能的解决方案:")
            print("1. 检查网络连接")
            print("2. 确认防火墙设置")
            print("3. 尝试使用代理")
        
        return False

async def main():
    """主函数"""
    success = await test_zhipu_ocr()
    
    if success:
        print("\n🎉 测试成功！您可以在项目中使用智谱AI OCR了。")
        print("\n📝 使用方法:")
        print("1. 在.env文件中设置: OCR_PROVIDER=zhipu")
        print("2. 运行项目: python main.py")
    else:
        print("\n❌ 测试失败，请检查配置后重试。")
    
    return 0 if success else 1

if __name__ == "__main__":
    # Windows兼容性
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)