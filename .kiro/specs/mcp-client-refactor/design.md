# MCP客户端重构设计文档

## 概述

本设计文档描述了如何重构现有的MCP客户端，从WebSocket连接方式改为streamableHttp方式，并集成FastMCP库来简化实现。重构将保持API兼容性，同时提供更好的错误处理和连接管理。

## 架构

### 当前架构问题
- 使用WebSocket连接，与mcp-chrome文档不符
- 手动实现MCP协议处理
- 错误处理不够完善
- 连接管理复杂

### 新架构设计

```mermaid
graph TB
    A[ReActAgent] --> B[ChromeMCPClient]
    B --> C[FastMCP Client]
    C --> D[HTTP Transport]
    D --> E[Chrome MCP Server]
    
    B --> F[Connection Manager]
    B --> G[Error Handler]
    B --> H[Request Builder]
    
    F --> I[Retry Logic]
    G --> J[Logging]
    H --> K[Method Mapping]
```

## 组件和接口

### 1. ChromeMCPClient (重构后)

```python
class ChromeMCPClient:
    """Chrome MCP客户端，使用FastMCP和streamableHttp"""
    
    def __init__(self, config: ChromeMCPConfig):
        self.config = config
        self.client: Optional[FastMCPClient] = None
        self.session: Optional[aiohttp.ClientSession] = None
        self.connected = False
    
    async def connect(self) -> bool:
        """连接到Chrome MCP Server"""
        
    async def disconnect(self):
        """断开连接"""
        
    async def send_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送MCP请求"""
        
    # 保持现有的浏览器操作方法
    async def navigate_to(self, url: str) -> Dict[str, Any]:
    async def click_element(self, selector: str) -> Dict[str, Any]:
    async def input_text(self, selector: str, text: str, clear: bool = True) -> Dict[str, Any]:
    # ... 其他方法保持不变
```

### 2. FastMCP集成

```python
from fastmcp import FastMCPClient
from fastmcp.transports import StreamableHttpTransport

class MCPTransportManager:
    """MCP传输管理器"""
    
    def __init__(self, config: ChromeMCPConfig):
        self.config = config
        self.transport = None
        self.client = None
    
    async def create_client(self) -> FastMCPClient:
        """创建FastMCP客户端"""
        transport = StreamableHttpTransport(
            base_url=f"http://{self.config.host}:{self.config.port}",
            timeout=self.config.timeout
        )
        
        client = FastMCPClient(transport=transport)
        return client
```

### 3. 连接管理器

```python
class ConnectionManager:
    """连接管理器，处理连接生命周期"""
    
    def __init__(self, client: ChromeMCPClient):
        self.client = client
        self.retry_count = 0
        self.max_retries = 3
        self.retry_delay = 2.0
    
    async def ensure_connected(self) -> bool:
        """确保连接可用"""
        
    async def reconnect(self) -> bool:
        """重新连接"""
        
    async def health_check(self) -> bool:
        """健康检查"""
```

### 4. 错误处理器

```python
class MCPErrorHandler:
    """MCP错误处理器"""
    
    @staticmethod
    def handle_connection_error(error: Exception) -> MCPConnectionError:
        """处理连接错误"""
        
    @staticmethod
    def handle_request_error(error: Exception) -> MCPRequestError:
        """处理请求错误"""
        
    @staticmethod
    def handle_timeout_error(error: Exception) -> MCPTimeoutError:
        """处理超时错误"""
```

## 数据模型

### 1. 配置模型更新

```python
class ChromeMCPConfig(BaseModel):
    host: str = os.getenv("CHROME_MCP_HOST", "127.0.0.1")
    port: int = int(os.getenv("CHROME_MCP_PORT", "3000"))
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 2.0
    use_https: bool = False
    auth_token: Optional[str] = None
    debug_mode: bool = False
```

### 2. 错误类型定义

```python
class MCPError(Exception):
    """MCP基础错误"""
    pass

class MCPConnectionError(MCPError):
    """MCP连接错误"""
    pass

class MCPRequestError(MCPError):
    """MCP请求错误"""
    pass

class MCPTimeoutError(MCPError):
    """MCP超时错误"""
    pass
```

### 3. 请求/响应模型

```python
class MCPRequest(BaseModel):
    method: str
    params: Dict[str, Any]
    id: Optional[str] = None

class MCPResponse(BaseModel):
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None
    id: Optional[str] = None
```

## 错误处理

### 1. 连接错误处理

```python
async def connect(self) -> bool:
    try:
        self.client = await self.transport_manager.create_client()
        await self.client.connect()
        self.connected = True
        logger.info("Connected to Chrome MCP Server")
        return True
    except ConnectionError as e:
        logger.error(f"Connection failed: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during connection: {e}")
        return False
```

### 2. 请求错误处理

```python
async def send_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
    try:
        if not self.connected:
            await self.ensure_connected()
        
        response = await self.client.call_method(method, params)
        return response
    except TimeoutError as e:
        raise MCPTimeoutError(f"Request timeout: {e}")
    except Exception as e:
        raise MCPRequestError(f"Request failed: {e}")
```

### 3. 重试机制

```python
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type((ConnectionError, TimeoutError))
)
async def _send_with_retry(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
    return await self.send_request(method, params)
```

## 测试策略

### 1. 单元测试

```python
class TestChromeMCPClient:
    async def test_connection_success(self):
        """测试成功连接"""
        
    async def test_connection_failure(self):
        """测试连接失败"""
        
    async def test_request_success(self):
        """测试成功请求"""
        
    async def test_request_timeout(self):
        """测试请求超时"""
        
    async def test_retry_mechanism(self):
        """测试重试机制"""
```

### 2. 集成测试

```python
class TestMCPIntegration:
    async def test_browser_navigation(self):
        """测试浏览器导航"""
        
    async def test_element_interaction(self):
        """测试元素交互"""
        
    async def test_form_filling(self):
        """测试表单填写"""
```

### 3. Mock测试

```python
class MockChromeMCPServer:
    """模拟Chrome MCP Server用于测试"""
    
    def __init__(self, port: int = 3000):
        self.port = port
        self.app = FastAPI()
        self.setup_routes()
    
    def setup_routes(self):
        """设置模拟路由"""
        
    async def start(self):
        """启动模拟服务器"""
        
    async def stop(self):
        """停止模拟服务器"""
```

## 迁移策略

### 1. 向后兼容

- 保持所有公共方法的签名不变
- 保持返回值格式一致
- 保持异常类型兼容

### 2. 渐进式迁移

1. **阶段1**: 创建新的MCP客户端实现
2. **阶段2**: 添加配置开关，支持新旧两种实现
3. **阶段3**: 默认使用新实现，保留旧实现作为fallback
4. **阶段4**: 完全移除旧实现

### 3. 配置迁移

```python
# 新增配置项，保持向后兼容
class ChromeMCPConfig(BaseModel):
    # 现有配置
    host: str = os.getenv("CHROME_MCP_HOST", "127.0.0.1")
    port: int = int(os.getenv("CHROME_MCP_PORT", "3000"))
    timeout: int = 30
    
    # 新增配置
    transport_type: str = "streamable_http"  # 新的传输类型
    max_retries: int = 3
    retry_delay: float = 2.0
    use_https: bool = False
    debug_mode: bool = False
```

## 性能考虑

### 1. 连接池

```python
class ConnectionPool:
    """连接池管理"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self.pool: List[ChromeMCPClient] = []
        self.semaphore = asyncio.Semaphore(max_connections)
    
    async def get_client(self) -> ChromeMCPClient:
        """获取客户端"""
        
    async def return_client(self, client: ChromeMCPClient):
        """归还客户端"""
```

### 2. 请求缓存

```python
class RequestCache:
    """请求结果缓存"""
    
    def __init__(self, ttl: int = 300):
        self.cache: Dict[str, Any] = {}
        self.ttl = ttl
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        
    async def set(self, key: str, value: Any):
        """设置缓存"""
```

## 安全考虑

### 1. 认证机制

```python
class MCPAuthenticator:
    """MCP认证器"""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token
    
    def add_auth_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """添加认证头"""
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        return headers
```

### 2. 请求验证

```python
def validate_request(self, method: str, params: Dict[str, Any]) -> bool:
    """验证请求参数"""
    # 验证方法名
    if method not in ALLOWED_METHODS:
        raise ValueError(f"Method {method} not allowed")
    
    # 验证参数
    if not isinstance(params, dict):
        raise ValueError("Params must be a dictionary")
    
    return True
```

## 监控和日志

### 1. 性能监控

```python
class MCPMetrics:
    """MCP性能指标"""
    
    def __init__(self):
        self.request_count = 0
        self.error_count = 0
        self.response_times: List[float] = []
    
    def record_request(self, duration: float, success: bool):
        """记录请求指标"""
        self.request_count += 1
        if not success:
            self.error_count += 1
        self.response_times.append(duration)
```

### 2. 结构化日志

```python
def log_request(self, method: str, params: Dict[str, Any], duration: float):
    """记录请求日志"""
    logger.info(
        "MCP request completed",
        extra={
            "method": method,
            "params_count": len(params),
            "duration_ms": duration * 1000,
            "client_id": self.client_id
        }
    )
```

这个设计提供了一个完整的MCP客户端重构方案，使用FastMCP和streamableHttp，同时保持向后兼容性和提供更好的错误处理。