# OCR服务集成指南

## 概述

本项目现在支持两种OCR服务：
1. **Tesseract OCR** - 本地免费OCR引擎
2. **智谱AI GLM-4.1V-Thinking** - 云端AI视觉模型

## 功能特性

### Tesseract OCR
- ✅ 完全免费
- ✅ 本地处理，数据安全
- ✅ 支持多种语言
- ❌ 识别准确率相对较低
- ❌ 对复杂布局支持有限

### 智谱AI GLM-4.1V-Thinking
- ✅ 高准确率识别
- ✅ 智能结构化数据提取
- ✅ 支持复杂布局和手写文字
- ✅ 多模态理解能力
- ❌ 需要API密钥和网络连接
- ❌ 按使用量付费

## 配置方法

### 1. 环境变量配置

在`.env`文件中设置：

```bash
# 选择OCR提供商
OCR_PROVIDER=tesseract  # 或 zhipu

# 智谱AI配置（仅在使用zhipu时需要）
ZHIPU_API_KEY=your_zhipu_api_key_here

# Tesseract路径（可选）
# TESSERACT_CMD=/usr/local/bin/tesseract
```

### 2. 获取智谱AI API密钥

1. 访问 [智谱AI开放平台](https://www.bigmodel.cn/)
2. 注册账号并实名认证
3. 创建API密钥
4. 充值账户余额

## 使用方法

### 基本使用

```python
import asyncio
from config import AgentConfig
from ocr_service import OCRService

async def main():
    config = AgentConfig()
    ocr = OCRService(config)
    
    # 提取文本
    text = await ocr.extract_text_from_image("receipt.jpg")
    print(text)
    
    # 提取结构化数据
    data = await ocr.extract_structured_data("receipt.jpg", "receipt")
    print(data)

asyncio.run(main())
```

### 在Agent中使用

OCR服务已集成到ReAct Agent中，支持自动识别附件：

```python
# 在聊天中发送附件
user_input = "附件:receipt.jpg 这是我的报销凭据"
response = await agent.process_user_input(user_input, ["receipt.jpg"])
```

## API接口

### 文本提取

```python
async def extract_text_from_image(self, image_path: str) -> str
```

### 结构化数据提取

```python
async def extract_structured_data(self, image_path: str, data_type: str) -> Dict[str, Any]
```

支持的数据类型：
- `receipt`: 发票/收据
- `id_card`: 身份证
- 其他: 返回原始文本

### Base64图像处理

```python
async def extract_text_from_base64(self, base64_data: str) -> str
```

## 测试工具

### 1. 基本OCR测试

```bash
python test.py ocr
```

### 2. 智谱AI专项测试

```bash
python test_zhipu_ocr.py
```

### 3. 对比测试

```bash
python ocr_example.py
```

## 性能对比

| 特性 | Tesseract | 智谱AI GLM-4.1V-Thinking |
|------|-----------|---------------------------|
| 成本 | 免费 | 付费 |
| 准确率 | 中等 | 高 |
| 速度 | 快 | 中等 |
| 结构化提取 | 基于规则 | AI智能提取 |
| 复杂布局 | 有限支持 | 优秀支持 |
| 手写识别 | 差 | 优秀 |
| 数据安全 | 本地处理 | 云端处理 |

## 最佳实践

### 1. 选择合适的OCR服务

- **开发测试阶段**: 使用Tesseract，免费且快速
- **生产环境**: 根据准确率要求选择
- **高准确率需求**: 使用智谱AI
- **数据敏感场景**: 使用Tesseract本地处理

### 2. 图像预处理

对于Tesseract，建议：
- 确保图像清晰
- 调整图像大小和对比度
- 去除噪声

对于智谱AI：
- 系统会自动优化图像
- 支持多种格式和分辨率

### 3. 错误处理

```python
try:
    result = await ocr.extract_structured_data(image_path, "receipt")
    if not result or "raw_text" in result:
        # 降级到文本提取
        text = await ocr.extract_text_from_image(image_path)
except Exception as e:
    logger.error(f"OCR failed: {e}")
    # 处理错误
```

## 故障排除

### 常见问题

1. **Tesseract未找到**
   ```bash
   # macOS
   brew install tesseract tesseract-lang
   
   # Ubuntu
   sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim
   ```

2. **智谱AI API错误**
   - 检查API密钥是否正确
   - 确认账户余额充足
   - 检查网络连接

3. **图像格式不支持**
   - 支持的格式：JPG, PNG, GIF, BMP
   - 建议使用JPG格式

### 调试模式

启用调试模式查看详细日志：

```bash
export DEBUG_MODE=true
python test.py ocr
```

## 扩展开发

### 添加新的OCR提供商

1. 创建新的客户端类
2. 在`OCRService`中添加支持
3. 更新配置文件
4. 添加测试用例

### 自定义数据提取

可以扩展`extract_structured_data`方法支持更多数据类型：

```python
async def extract_structured_data(self, image_path: str, data_type: str):
    if data_type == "custom_type":
        # 自定义提取逻辑
        pass
```

## 更新日志

- **v1.0**: 添加智谱AI GLM-4.1V-Thinking支持
- **v1.0**: 重构OCR服务架构
- **v1.0**: 支持多OCR提供商切换
- **v1.0**: 添加异步支持
- **v1.0**: 完善错误处理和日志