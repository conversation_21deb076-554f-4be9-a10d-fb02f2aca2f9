# 项目结构说明

## 目录结构

```
form-filling-agent/
├── src/                          # 源代码目录
│   ├── __init__.py
│   ├── agents/                   # 智能代理模块
│   │   ├── __init__.py
│   │   └── react_agent.py        # ReAct架构智能代理
│   ├── clients/                  # 外部服务客户端
│   │   ├── __init__.py
│   │   ├── mcp_client.py         # Chrome MCP客户端
│   │   ├── siliconflow_client.py # 硅基流动API客户端
│   │   └── zhipu_ocr_client.py   # 智谱AI OCR客户端
│   ├── services/                 # 业务服务层
│   │   ├── __init__.py
│   │   └── ocr_service.py        # OCR服务统一接口
│   ├── config/                   # 配置模块
│   │   ├── __init__.py
│   │   └── settings.py           # 系统配置和表单配置
│   ├── utils/                    # 工具函数（预留）
│   └── models/                   # 数据模型（预留）
├── tests/                        # 测试文件
│   ├── __init__.py
│   ├── test.py                   # 主要功能测试
│   └── test_zhipu_ocr.py         # 智谱AI OCR专项测试
├── examples/                     # 使用示例
│   └── ocr_example.py            # OCR服务对比示例
├── docs/                         # 文档目录
│   ├── PROJECT_STRUCTURE.md      # 项目结构说明
│   └── OCR_INTEGRATION_GUIDE.md  # OCR集成指南
├── scripts/                      # 脚本目录（预留）
├── logs/                         # 日志目录
├── venv/                         # 虚拟环境
├── main.py                       # 主程序入口
├── api_server.py                 # Web API服务器
├── requirements.txt              # Python依赖
├── .env                          # 环境变量配置
├── .env.example                  # 环境变量示例
├── README.md                     # 项目说明
└── test_receipt.jpg              # 测试图像文件
```

## 模块说明

### 1. src/agents/ - 智能代理模块
- **react_agent.py**: 实现ReAct（推理-行动-观察）架构的智能代理
- 负责处理用户输入、意图识别、动作规划和执行

### 2. src/clients/ - 外部服务客户端
- **mcp_client.py**: Chrome MCP Server客户端，用于浏览器自动化
- **siliconflow_client.py**: 硅基流动API客户端，提供大模型能力
- **zhipu_ocr_client.py**: 智谱AI GLM-4.1V-Thinking OCR客户端

### 3. src/services/ - 业务服务层
- **ocr_service.py**: OCR服务统一接口，支持多种OCR提供商切换

### 4. src/config/ - 配置模块
- **settings.py**: 系统配置类和表单配置定义

### 5. tests/ - 测试模块
- **test.py**: 主要功能测试套件
- **test_zhipu_ocr.py**: 智谱AI OCR专项测试

### 6. examples/ - 示例代码
- **ocr_example.py**: 演示不同OCR服务的使用和对比

### 7. docs/ - 文档
- **PROJECT_STRUCTURE.md**: 项目结构说明
- **OCR_INTEGRATION_GUIDE.md**: OCR集成详细指南

## 设计原则

### 1. 模块化设计
- 按功能职责划分模块
- 每个模块有明确的边界和接口
- 便于维护和扩展

### 2. 分层架构
```
应用层 (main.py, api_server.py)
    ↓
业务层 (agents/, services/)
    ↓
客户端层 (clients/)
    ↓
配置层 (config/)
```

### 3. 依赖注入
- 通过配置对象注入依赖
- 便于测试和模块替换

### 4. 接口统一
- 相同类型的服务提供统一接口
- 如OCR服务支持多种提供商但接口一致

## 导入规范

### 1. 相对导入
模块内部使用相对导入：
```python
from ..config import AgentConfig
from ..clients import ChromeMCPClient
```

### 2. 绝对导入
外部使用绝对导入：
```python
from src.config import AgentConfig
from src.agents import ReActAgent
```

### 3. 测试和示例
测试和示例文件需要添加路径：
```python
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config import AgentConfig
```

## 扩展指南

### 1. 添加新的客户端
1. 在`src/clients/`目录创建新文件
2. 在`src/clients/__init__.py`中导出
3. 在相关服务中集成

### 2. 添加新的服务
1. 在`src/services/`目录创建新文件
2. 在`src/services/__init__.py`中导出
3. 在代理中使用

### 3. 添加新的配置
1. 在`src/config/settings.py`中添加配置类
2. 在`src/config/__init__.py`中导出
3. 在相关模块中使用

### 4. 添加测试
1. 在`tests/`目录创建测试文件
2. 使用统一的导入方式
3. 遵循测试命名规范

## 最佳实践

### 1. 代码组织
- 每个文件职责单一
- 类和函数命名清晰
- 适当的注释和文档

### 2. 错误处理
- 统一的异常处理机制
- 详细的日志记录
- 优雅的降级策略

### 3. 配置管理
- 环境变量配置
- 配置验证
- 默认值设置

### 4. 测试覆盖
- 单元测试
- 集成测试
- 端到端测试