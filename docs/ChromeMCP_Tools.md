=== 可用的Chrome MCP工具 ===
正在获取工具列表...
✅ 找到 23 个可用工具：

 1. get_windows_and_tabs
    描述: Get all currently open browser windows and tabs
    参数: 无

 2. chrome_navigate
    描述: Navigate to a URL or refresh the current tab
    参数:
      - url (string) (可选): URL to navigate to the website specified
      - newWindow (boolean) (可选): Create a new window to navigate to the URL or not. Defaults to false
      - width (number) (可选): Viewport width in pixels (default: 1280)
      - height (number) (可选): Viewport height in pixels (default: 720)
      - refresh (boolean) (可选): Refresh the current active tab instead of navigating to a URL. When true, the url parameter is ignored. Defaults to false

 3. chrome_screenshot
    描述: Take a screenshot of the current page or a specific element(if you want to see the page, recommend to use chrome_get_web_content first)
    参数:
      - name (string) (可选): Name for the screenshot, if saving as PNG
      - selector (string) (可选): CSS selector for element to screenshot
      - width (number) (可选): Width in pixels (default: 800)
      - height (number) (可选): Height in pixels (default: 600)
      - storeBase64 (boolean) (可选): return screenshot in base64 format (default: false) if you want to see the page, recommend set this to be true
      - fullPage (boolean) (可选): Store screenshot of the entire page (default: true)
      - savePng (boolean) (可选): Save screenshot as PNG file (default: true)，if you want to see the page, recommend set this to be false, and set storeBase64 to be true

 4. chrome_close_tabs
    描述: Close one or more browser tabs
    参数:
      - tabIds (array) (可选): Array of tab IDs to close. If not provided, will close the active tab.
      - url (string) (可选): Close tabs matching this URL. Can be used instead of tabIds.

 5. chrome_go_back_or_forward
    描述: Navigate back or forward in browser history
    参数:
      - isForward (boolean) (可选): Go forward in history if true, go back if false (default: false)

 6. chrome_get_web_content
    描述: Fetch content from a web page
    参数:
      - url (string) (可选): URL to fetch content from. If not provided, uses the current active tab
      - htmlContent (boolean) (可选): Get the visible HTML content of the page. If true, textContent will be ignored (default: false)
      - textContent (boolean) (可选): Get the visible text content of the page with metadata. Ignored if htmlContent is true (default: true)
      - selector (string) (可选): CSS selector to get content from a specific element. If provided, only content from this element will be returned

 7. chrome_click_element
    描述: Click on an element in the current page or at specific coordinates
    参数:
      - selector (string) (可选): CSS selector for the element to click. Either selector or coordinates must be provided. if coordinates are not provided, the selector must be provided.
      - coordinates (object) (可选): Coordinates to click at (relative to viewport). If provided, takes precedence over selector.
      - waitForNavigation (boolean) (可选): Wait for page navigation to complete after click (default: false)
      - timeout (number) (可选): Timeout in milliseconds for waiting for the element or navigation (default: 5000)

 8. chrome_fill_or_select
    描述: Fill a form element or select an option with the specified value
    参数:
      - selector (string) (必需): CSS selector for the input element to fill or select
      - value (string) (必需): Value to fill or select into the element

 9. chrome_get_interactive_elements
    描述: Get interactive elements from the current page
    参数:
      - textQuery (string) (可选): Text to search for within interactive elements (fuzzy search)
      - selector (string) (可选): CSS selector to filter interactive elements. Takes precedence over textQuery if both are provided.
      - includeCoordinates (boolean) (可选): Include element coordinates in the response (default: true)

10. chrome_network_request
    描述: Send a network request from the browser with cookies and other browser context
    参数:
      - url (string) (必需): URL to send the request to
      - method (string) (可选): HTTP method to use (default: GET)
      - headers (object) (可选): Headers to include in the request
      - body (string) (可选): Body of the request (for POST, PUT, etc.)
      - timeout (number) (可选): Timeout in milliseconds (default: 30000)

11. chrome_network_debugger_start
    描述: Start capturing network requests from a web page using Chrome Debugger API（with responseBody）
    参数:
      - url (string) (可选): URL to capture network requests from. If not provided, uses the current active tab

12. chrome_network_debugger_stop
    描述: Stop capturing network requests using Chrome Debugger API and return the captured data
    参数: 无

13. chrome_network_capture_start
    描述: Start capturing network requests from a web page using Chrome webRequest API(without responseBody)
    参数:
      - url (string) (可选): URL to capture network requests from. If not provided, uses the current active tab

14. chrome_network_capture_stop
    描述: Stop capturing network requests using webRequest API and return the captured data
    参数: 无

15. chrome_keyboard
    描述: Simulate keyboard events in the browser
    参数:
      - keys (string) (必需): Keys to simulate (e.g., "Enter", "Ctrl+C", "A,B,C" for sequence)
      - selector (string) (可选): CSS selector for the element to send keyboard events to (optional, defaults to active element)
      - delay (number) (可选): Delay between key sequences in milliseconds (optional, default: 0)

16. chrome_history
    描述: Retrieve and search browsing history from Chrome
    参数:
      - text (string) (可选): Text to search for in history URLs and titles. Leave empty to retrieve all history entries within the time range.
      - startTime (string) (可选): Start time as a date string. Supports ISO format (e.g., "2023-10-01", "2023-10-01T14:30:00"), relative times (e.g., "1 day ago", "2 weeks ago", "3 months ago", "1 year ago"), and special keywords ("now", "today", "yesterday"). Default: 24 hours ago
      - endTime (string) (可选): End time as a date string. Supports ISO format (e.g., "2023-10-31", "2023-10-31T14:30:00"), relative times (e.g., "1 day ago", "2 weeks ago", "3 months ago", "1 year ago"), and special keywords ("now", "today", "yesterday"). Default: current time
      - maxResults (number) (可选): Maximum number of history entries to return. Use this to limit results for performance or to focus on the most relevant entries. (default: 100)
      - excludeCurrentTabs (boolean) (可选): When set to true, filters out URLs that are currently open in any browser tab. Useful for finding pages you've visited but don't have open anymore. (default: false)

17. chrome_bookmark_search
    描述: Search Chrome bookmarks by title and URL
    参数:
      - query (string) (可选): Search query to match against bookmark titles and URLs. Leave empty to retrieve all bookmarks.
      - maxResults (number) (可选): Maximum number of bookmarks to return (default: 50)
      - folderPath (string) (可选): Optional folder path or ID to limit search to a specific bookmark folder. Can be a path string (e.g., "Work/Projects") or a folder ID.

18. chrome_bookmark_add
    描述: Add a new bookmark to Chrome
    参数:
      - url (string) (可选): URL to bookmark. If not provided, uses the current active tab URL.
      - title (string) (可选): Title for the bookmark. If not provided, uses the page title from the URL.
      - parentId (string) (可选): Parent folder path or ID to add the bookmark to. Can be a path string (e.g., "Work/Projects") or a folder ID. If not provided, adds to the "Bookmarks Bar" folder.
      - createFolder (boolean) (可选): Whether to create the parent folder if it does not exist (default: false)

19. chrome_bookmark_delete
    描述: Delete a bookmark from Chrome
    参数:
      - bookmarkId (string) (可选): ID of the bookmark to delete. Either bookmarkId or url must be provided.
      - url (string) (可选): URL of the bookmark to delete. Used if bookmarkId is not provided.
      - title (string) (可选): Title of the bookmark to help with matching when deleting by URL.

20. search_tabs_content
    描述: search for related content from the currently open tab and return the corresponding web pages.
    参数:
      - query (string) (必需): the query to search for related content.

21. chrome_inject_script
    描述: inject the user-specified content script into the webpage. By default, inject into the currently active tab
    参数:
      - url (string) (可选): If a URL is specified, inject the script into the webpage corresponding to the URL.
      - type (string) (必需): the javaScript world for a script to execute within. must be ISOLATED or MAIN
      - jsScript (string) (必需): the content script to inject

22. chrome_send_command_to_inject_script
    描述: if the script injected using chrome_inject_script listens for user-defined events, this tool can be used to trigger those events
    参数:
      - tabId (number) (可选): the tab where you previously injected the script(if not provided,  use the currently active tab)
      - eventName (string) (必需): the eventName your injected content script listen for
      - payload (string) (可选): the payload passed to event, must be a json string

23. chrome_console
    描述: Capture and retrieve all console output from the current active browser tab/page. This captures console messages that existed before the tool was called.
    参数:
      - url (string) (可选): URL to navigate to and capture console from. If not provided, uses the current active tab
      - includeExceptions (boolean) (可选): Include uncaught exceptions in the output (default: true)
      - maxMessages (number) (可选): Maximum number of console messages to capture (default: 100)
