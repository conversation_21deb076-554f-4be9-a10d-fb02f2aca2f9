"""
MCP客户端错误处理模块
"""

from typing import Optional, Dict, Any
from loguru import logger


class MCPError(Exception):
    """MCP基础异常类"""
    
    def __init__(self, message: str, code: Optional[int] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.code = code
        self.details = details or {}
    
    def __str__(self):
        if self.code:
            return f"[{self.code}] {self.message}"
        return self.message


class MCPConnectionError(MCPError):
    """MCP连接错误"""
    
    def __init__(self, message: str, server_url: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.server_url = server_url


class MCPRequestError(MCPError):
    """MCP请求错误"""
    
    def __init__(self, message: str, method: Optional[str] = None, params: Optional[Dict[str, Any]] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.method = method
        self.params = params


class MCPTimeoutError(MCPError):
    """MCP超时错误"""
    
    def __init__(self, message: str, timeout: Optional[float] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.timeout = timeout


class MCPAuthenticationError(MCPError):
    """MCP认证错误"""
    pass


class MCPServerError(MCPError):
    """MCP服务器错误"""
    pass


class MCPErrorHandler:
    """MCP错误处理器"""
    
    @staticmethod
    def handle_connection_error(error: Exception, server_url: str) -> MCPConnectionError:
        """处理连接错误"""
        if "timeout" in str(error).lower():
            return MCPTimeoutError(
                f"Connection timeout to {server_url}: {str(error)}",
                details={"server_url": server_url, "original_error": str(error)}
            )
        elif "authentication" in str(error).lower() or "unauthorized" in str(error).lower():
            return MCPAuthenticationError(
                f"Authentication failed for {server_url}: {str(error)}",
                details={"server_url": server_url, "original_error": str(error)}
            )
        else:
            return MCPConnectionError(
                f"Failed to connect to {server_url}: {str(error)}",
                server_url=server_url,
                details={"original_error": str(error)}
            )
    
    @staticmethod
    def handle_request_error(error: Exception, method: str, params: Optional[Dict[str, Any]] = None) -> MCPRequestError:
        """处理请求错误"""
        if "timeout" in str(error).lower():
            return MCPTimeoutError(
                f"Request timeout for method {method}: {str(error)}",
                details={"method": method, "params": params, "original_error": str(error)}
            )
        elif "server error" in str(error).lower() or "500" in str(error):
            return MCPServerError(
                f"Server error for method {method}: {str(error)}",
                code=500,
                details={"method": method, "params": params, "original_error": str(error)}
            )
        else:
            return MCPRequestError(
                f"Request failed for method {method}: {str(error)}",
                method=method,
                params=params,
                details={"original_error": str(error)}
            )
    
    @staticmethod
    def log_error(error: MCPError, context: Optional[str] = None):
        """记录错误日志"""
        context_str = f" [{context}]" if context else ""
        
        if isinstance(error, MCPConnectionError):
            logger.error(f"MCP Connection Error{context_str}: {error.message}")
            if error.server_url:
                logger.error(f"  Server URL: {error.server_url}")
        
        elif isinstance(error, MCPRequestError):
            logger.error(f"MCP Request Error{context_str}: {error.message}")
            if error.method:
                logger.error(f"  Method: {error.method}")
            if error.params:
                logger.error(f"  Params: {error.params}")
        
        elif isinstance(error, MCPTimeoutError):
            logger.error(f"MCP Timeout Error{context_str}: {error.message}")
            if error.timeout:
                logger.error(f"  Timeout: {error.timeout}s")
        
        else:
            logger.error(f"MCP Error{context_str}: {error.message}")
        
        if error.details:
            logger.debug(f"  Error details: {error.details}")
    
    @staticmethod
    def is_retryable_error(error: Exception) -> bool:
        """判断错误是否可重试"""
        if isinstance(error, (MCPTimeoutError, MCPConnectionError)):
            return True
        
        if isinstance(error, MCPServerError):
            # 5xx服务器错误通常可以重试
            return True
        
        if isinstance(error, MCPAuthenticationError):
            # 认证错误通常不应该重试
            return False
        
        # 检查错误消息中的关键词
        error_str = str(error).lower()
        retryable_keywords = ["timeout", "connection", "network", "temporary", "unavailable"]
        non_retryable_keywords = ["authentication", "unauthorized", "forbidden", "not found"]
        
        if any(keyword in error_str for keyword in non_retryable_keywords):
            return False
        
        if any(keyword in error_str for keyword in retryable_keywords):
            return True
        
        return False