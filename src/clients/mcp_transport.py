"""
MCP传输管理器
使用HTTP请求直接与Chrome MCP Server通信
"""

import asyncio
import json
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager
from loguru import logger
import httpx

from ..config import ChromeMCPConfig
from .mcp_errors import (
    MCPError, MCPConnectionError, MCPRequestError, 
    MCPTimeoutError, MCPErrorHandler
)


class MCPTransportManager:
    """MCP传输管理器 - 使用HTTP请求直接通信"""
    
    def __init__(self, config: ChromeMCPConfig):
        self.config = config
        self.session_id: Optional[str] = None
        self.http_client: Optional[httpx.AsyncClient] = None
        self._connection_lock = asyncio.Lock()
        self._is_connected = False
        self._request_id = 0
        
    async def connect(self) -> bool:
        """连接到MCP服务器"""
        async with self._connection_lock:
            if self._is_connected:
                return True
            
            try:
                # 创建HTTP客户端
                self.http_client = httpx.AsyncClient(
                    timeout=httpx.Timeout(self.config.timeout),
                    headers=self._get_base_headers()
                )
                
                # 发送初始化请求
                await self._initialize()
                
                # 发送initialized通知
                await self._send_initialized()
                
                self._is_connected = True
                logger.info(f"Successfully connected to MCP server at {self.config.mcp_url}")
                return True
                
            except Exception as e:
                error = MCPErrorHandler.handle_connection_error(e, self.config.mcp_url)
                MCPErrorHandler.log_error(error, "connect")
                await self._cleanup()
                raise error
    
    async def _initialize(self):
        """初始化MCP会话"""
        init_request = {
            "jsonrpc": "2.0",
            "id": self._next_request_id(),
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "roots": {
                        "listChanged": True
                    },
                    "sampling": {}
                },
                "clientInfo": {
                    "name": "chrome-mcp-client",
                    "version": "1.0.0"
                }
            }
        }
        
        if self.config.debug_mode:
            logger.debug(f"Sending initialize request: {json.dumps(init_request, indent=2)}")
        
        response = await self.http_client.post(
            self.config.mcp_url,
            json=init_request,
            timeout=self.config.timeout
        )
        
        if response.status_code != 200:
            raise MCPConnectionError(f"Initialize failed with status {response.status_code}: {response.text}")
        
        # 提取会话ID
        self.session_id = response.headers.get('mcp-session-id')
        if not self.session_id:
            raise MCPConnectionError("No session ID received from server")
        
        if self.config.debug_mode:
            logger.debug(f"Initialize successful, session ID: {self.session_id}")
            logger.debug(f"Initialize response: {response.text}")
    
    async def _send_initialized(self):
        """发送initialized通知"""
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "initialized",
            "params": {}
        }
        
        if self.config.debug_mode:
            logger.debug("Sending initialized notification")
        
        response = await self.http_client.post(
            self.config.mcp_url,
            json=initialized_notification,
            headers=self._get_session_headers(),
            timeout=self.config.timeout
        )
        
        if response.status_code not in [200, 202]:
            logger.warning(f"Initialized notification returned status {response.status_code}: {response.text}")
        
        if self.config.debug_mode:
            logger.debug(f"Initialized notification response: {response.status_code}")
    
    def _get_base_headers(self) -> Dict[str, str]:
        """获取基础请求头"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream",
            "Cache-Control": "no-cache",
            "User-Agent": "MCP-Client/1.0"
        }
        
        if self.config.auth_token:
            headers["Authorization"] = f"Bearer {self.config.auth_token}"
        
        return headers
    
    def _get_session_headers(self) -> Dict[str, str]:
        """获取带会话ID的请求头"""
        headers = self._get_base_headers()
        if self.session_id:
            headers["mcp-session-id"] = self.session_id
        return headers
    
    def _next_request_id(self) -> int:
        """获取下一个请求ID"""
        self._request_id += 1
        return self._request_id
    
    def _parse_sse_response(self, response_text: str) -> Dict[str, Any]:
        """解析SSE格式的响应"""
        try:
            # SSE格式: "event: message\ndata: {json_data}"
            if response_text.startswith('event: message\ndata: '):
                json_data = response_text.split('data: ')[1].strip()
                return json.loads(json_data)
            else:
                # 尝试直接解析JSON
                return json.loads(response_text)
        except json.JSONDecodeError as e:
            raise MCPRequestError(f"Failed to parse response: {e}")
    
    async def _send_request(self, method: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送MCP请求"""
        if not self._is_connected:
            raise MCPConnectionError("Not connected to MCP server")
        
        request = {
            "jsonrpc": "2.0",
            "id": self._next_request_id(),
            "method": method,
            "params": params or {}
        }
        
        if self.config.debug_mode:
            logger.debug(f"Sending request: {method} with params: {params}")
        
        try:
            response = await self.http_client.post(
                self.config.mcp_url,
                json=request,
                headers=self._get_session_headers(),
                timeout=self.config.timeout
            )
            
            if response.status_code != 200:
                raise MCPRequestError(f"Request failed with status {response.status_code}: {response.text}")
            
            # 解析SSE格式的响应
            response_data = self._parse_sse_response(response.text)
            
            if self.config.debug_mode:
                logger.debug(f"Request response: {response_data}")
            
            # 检查错误
            if "error" in response_data:
                error = response_data["error"]
                raise MCPRequestError(f"MCP error {error.get('code', 'unknown')}: {error.get('message', 'Unknown error')}")
            
            return response_data.get("result", {})
            
        except httpx.TimeoutException:
            raise MCPTimeoutError(f"Request timeout for {method}")
        except MCPError:
            raise
        except Exception as e:
            raise MCPRequestError(f"Request failed: {str(e)}")
    
    async def disconnect(self):
        """断开连接"""
        async with self._connection_lock:
            await self._cleanup()
            logger.info("Disconnected from MCP server")
    
    async def _cleanup(self):
        """清理资源"""
        if self.http_client:
            await self.http_client.aclose()
            self.http_client = None
        self.session_id = None
        self._is_connected = False
    
    @property
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._is_connected and self.session_id is not None
    
    async def health_check(self) -> bool:
        """健康检查"""
        if not self.is_connected:
            return False
        
        try:
            # 尝试列出工具作为健康检查
            await self._send_request("tools/list")
            return True
        except Exception as e:
            logger.debug(f"Health check failed: {e}")
            return False
    
    @asynccontextmanager
    async def ensure_connection(self):
        """确保连接可用的上下文管理器"""
        if not self.is_connected:
            await self.connect()
        
        try:
            yield
        except Exception as e:
            # 如果是连接相关错误，标记为未连接
            if isinstance(e, (MCPConnectionError, MCPTimeoutError)):
                self._is_connected = False
            raise
    
    async def call_tool(self, name: str, arguments: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            result = await self._send_request("tools/call", {
                "name": name,
                "arguments": arguments or {}
            })
            
            if self.config.debug_mode:
                logger.debug(f"Tool call result for {name}: {result}")
            
            return {"result": result}
            
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, name, arguments)
            MCPErrorHandler.log_error(error, f"call_tool:{name}")
            raise error
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """列出可用工具"""
        try:
            result = await self._send_request("tools/list")
            return result.get("tools", [])
            
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "list_tools")
            MCPErrorHandler.log_error(error, "list_tools")
            raise error
    
    async def get_server_info(self) -> Dict[str, Any]:
        """获取服务器信息"""
        try:
            # 获取服务器信息
            info = {
                "server_url": self.config.mcp_url,
                "session_id": self.session_id,
                "transport_type": self.config.transport_type,
                "connected": self.is_connected,
                "config": {
                    "timeout": self.config.timeout,
                    "max_retries": self.config.max_retries,
                    "debug_mode": self.config.debug_mode
                }
            }
            
            # 尝试获取可用工具列表
            if self.is_connected:
                try:
                    tools = await self.list_tools()
                    info["available_tools"] = [tool.get("name", "unknown") for tool in tools]
                except Exception as e:
                    logger.debug(f"Failed to get tools list: {e}")
                    info["available_tools"] = []
            else:
                info["available_tools"] = []
            
            return info
            
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "get_server_info")
            MCPErrorHandler.log_error(error, "get_server_info")
            raise error