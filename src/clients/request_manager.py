"""
MCP请求管理器
处理请求发送、重试、超时和日志记录
"""

import asyncio
import time
import uuid
from typing import Dict, Any, Optional, List, Callable, Awaitable
from contextlib import asynccontextmanager
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ..config import ChromeMCPConfig
from .mcp_errors import MCPError, MCPConnectionError, MCPRequestError, MCPTimeoutError, MCPErrorHandler


class RequestMetrics:
    """请求指标收集器"""
    
    def __init__(self):
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.timeout_requests = 0
        self.retry_requests = 0
        self.response_times: List[float] = []
        self.error_counts: Dict[str, int] = {}
        
    def record_request(self, duration: float, success: bool, error_type: Optional[str] = None, retries: int = 0):
        """记录请求指标"""
        self.total_requests += 1
        self.response_times.append(duration)
        
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
            if error_type:
                self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        if retries > 0:
            self.retry_requests += 1
        
        # 保持最近1000次请求的响应时间
        if len(self.response_times) > 1000:
            self.response_times = self.response_times[-1000:]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.response_times:
            return {
                "total_requests": self.total_requests,
                "successful_requests": self.successful_requests,
                "failed_requests": self.failed_requests,
                "timeout_requests": self.timeout_requests,
                "retry_requests": self.retry_requests,
                "success_rate": 0.0,
                "average_response_time_ms": 0.0,
                "error_counts": self.error_counts
            }
        
        avg_response_time = sum(self.response_times) / len(self.response_times)
        success_rate = self.successful_requests / self.total_requests if self.total_requests > 0 else 0.0
        
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "timeout_requests": self.timeout_requests,
            "retry_requests": self.retry_requests,
            "success_rate": success_rate,
            "average_response_time_ms": avg_response_time * 1000,
            "min_response_time_ms": min(self.response_times) * 1000,
            "max_response_time_ms": max(self.response_times) * 1000,
            "recent_response_times_ms": [t * 1000 for t in self.response_times[-10:]],
            "error_counts": self.error_counts
        }


class RequestLogger:
    """请求日志记录器"""
    
    def __init__(self, config: ChromeMCPConfig):
        self.config = config
        self.debug_mode = config.debug_mode
    
    def log_request_start(self, request_id: str, method: str, params: Dict[str, Any]):
        """记录请求开始"""
        if self.debug_mode:
            logger.debug(
                f"MCP Request Start [{request_id}]",
                extra={
                    "request_id": request_id,
                    "method": method,
                    "params_count": len(params),
                    "event": "request_start"
                }
            )
    
    def log_request_success(self, request_id: str, method: str, duration: float, result_size: int):
        """记录请求成功"""
        if self.debug_mode:
            logger.debug(
                f"MCP Request Success [{request_id}]",
                extra={
                    "request_id": request_id,
                    "method": method,
                    "duration_ms": duration * 1000,
                    "result_size": result_size,
                    "event": "request_success"
                }
            )
        else:
            logger.info(f"MCP request {method} completed in {duration*1000:.1f}ms")
    
    def log_request_error(self, request_id: str, method: str, duration: float, error: Exception, retry_count: int = 0):
        """记录请求错误"""
        logger.error(
            f"MCP Request Error [{request_id}]",
            extra={
                "request_id": request_id,
                "method": method,
                "duration_ms": duration * 1000,
                "error_type": type(error).__name__,
                "error_message": str(error),
                "retry_count": retry_count,
                "event": "request_error"
            }
        )
    
    def log_request_timeout(self, request_id: str, method: str, timeout: float):
        """记录请求超时"""
        logger.warning(
            f"MCP Request Timeout [{request_id}]",
            extra={
                "request_id": request_id,
                "method": method,
                "timeout_seconds": timeout,
                "event": "request_timeout"
            }
        )
    
    def log_request_retry(self, request_id: str, method: str, retry_count: int, delay: float):
        """记录请求重试"""
        logger.warning(
            f"MCP Request Retry [{request_id}] - Attempt {retry_count}",
            extra={
                "request_id": request_id,
                "method": method,
                "retry_count": retry_count,
                "retry_delay": delay,
                "event": "request_retry"
            }
        )


class RequestManager:
    """MCP请求管理器"""
    
    def __init__(self, config: ChromeMCPConfig):
        self.config = config
        self.metrics = RequestMetrics()
        self.logger = RequestLogger(config)
        self._request_callbacks: Dict[str, Callable[[str, Dict[str, Any]], Awaitable[Dict[str, Any]]]] = {}
        
    def register_request_handler(self, name: str, handler: Callable[[str, Dict[str, Any]], Awaitable[Dict[str, Any]]]):
        """注册请求处理器"""
        self._request_callbacks[name] = handler
    
    async def send_request(
        self, 
        method: str, 
        params: Optional[Dict[str, Any]] = None,
        timeout: Optional[float] = None,
        retry_count: Optional[int] = None
    ) -> Dict[str, Any]:
        """发送MCP请求（带重试和日志）"""
        request_id = str(uuid.uuid4())[:8]
        params = params or {}
        timeout = timeout or self.config.timeout
        retry_count = retry_count or self.config.max_retries
        
        start_time = time.time()
        self.logger.log_request_start(request_id, method, params)
        
        try:
            # 使用重试机制发送请求
            result = await self._send_with_retry(
                request_id=request_id,
                method=method,
                params=params,
                timeout=timeout,
                max_retries=retry_count
            )
            
            # 记录成功指标
            duration = time.time() - start_time
            result_size = len(str(result)) if result else 0
            self.metrics.record_request(duration, True, retries=0)
            self.logger.log_request_success(request_id, method, duration, result_size)
            
            return result
            
        except Exception as e:
            # 记录失败指标
            duration = time.time() - start_time
            error_type = type(e).__name__
            self.metrics.record_request(duration, False, error_type)
            self.logger.log_request_error(request_id, method, duration, e)
            raise
    
    @retry(
        stop=stop_after_attempt(1),  # 这里设为1，实际重试在_send_with_retry中处理
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((MCPConnectionError, MCPTimeoutError))
    )
    async def _send_with_retry(
        self,
        request_id: str,
        method: str,
        params: Dict[str, Any],
        timeout: float,
        max_retries: int
    ) -> Dict[str, Any]:
        """带重试机制的请求发送"""
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    # 计算重试延迟
                    delay = min(2 ** attempt, 10)  # 指数退避，最大10秒
                    self.logger.log_request_retry(request_id, method, attempt, delay)
                    await asyncio.sleep(delay)
                
                # 发送实际请求
                result = await self._send_single_request(method, params, timeout)
                return result
                
            except MCPTimeoutError as e:
                last_exception = e
                self.metrics.timeout_requests += 1
                self.logger.log_request_timeout(request_id, method, timeout)
                
                if attempt == max_retries:
                    break
                    
            except (MCPConnectionError, MCPRequestError) as e:
                last_exception = e
                
                if attempt == max_retries:
                    break
                    
            except Exception as e:
                # 对于其他类型的错误，不重试
                raise MCPRequestError(f"Request failed: {str(e)}")
        
        # 所有重试都失败了
        if last_exception:
            raise last_exception
        else:
            raise MCPRequestError("Request failed after all retries")
    
    async def _send_single_request(self, method: str, params: Dict[str, Any], timeout: float) -> Dict[str, Any]:
        """发送单个请求"""
        # 查找注册的请求处理器
        if "transport" not in self._request_callbacks:
            raise MCPRequestError("No transport handler registered")
        
        handler = self._request_callbacks["transport"]
        
        try:
            # 使用超时控制
            result = await asyncio.wait_for(
                handler(method, params),
                timeout=timeout
            )
            return result
            
        except asyncio.TimeoutError:
            raise MCPTimeoutError(f"Request timeout after {timeout}s", timeout=timeout)
        except Exception as e:
            if isinstance(e, MCPError):
                raise
            else:
                raise MCPRequestError(f"Request failed: {str(e)}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取请求指标"""
        return self.metrics.get_stats()
    
    def reset_metrics(self):
        """重置指标"""
        self.metrics = RequestMetrics()


class RequestCache:
    """请求缓存管理器（可选功能）"""
    
    def __init__(self, ttl: int = 300, max_size: int = 1000):
        self.ttl = ttl  # 缓存生存时间（秒）
        self.max_size = max_size
        self.cache: Dict[str, Dict[str, Any]] = {}
        self._access_times: Dict[str, float] = {}
    
    def _generate_key(self, method: str, params: Dict[str, Any]) -> str:
        """生成缓存键"""
        import hashlib
        import json
        
        # 创建一个稳定的键
        key_data = {
            "method": method,
            "params": sorted(params.items()) if params else []
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, method: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """获取缓存的结果"""
        key = self._generate_key(method, params)
        
        if key not in self.cache:
            return None
        
        # 检查是否过期
        current_time = time.time()
        if current_time - self._access_times[key] > self.ttl:
            self._remove(key)
            return None
        
        # 更新访问时间
        self._access_times[key] = current_time
        return self.cache[key].copy()
    
    def set(self, method: str, params: Dict[str, Any], result: Dict[str, Any]):
        """设置缓存"""
        key = self._generate_key(method, params)
        current_time = time.time()
        
        # 如果缓存已满，移除最旧的条目
        if len(self.cache) >= self.max_size:
            self._evict_oldest()
        
        self.cache[key] = result.copy()
        self._access_times[key] = current_time
    
    def _remove(self, key: str):
        """移除缓存条目"""
        self.cache.pop(key, None)
        self._access_times.pop(key, None)
    
    def _evict_oldest(self):
        """移除最旧的缓存条目"""
        if not self._access_times:
            return
        
        oldest_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        self._remove(oldest_key)
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self._access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        current_time = time.time()
        expired_count = sum(
            1 for access_time in self._access_times.values()
            if current_time - access_time > self.ttl
        )
        
        return {
            "cache_size": len(self.cache),
            "max_size": self.max_size,
            "expired_entries": expired_count,
            "hit_rate": 0.0  # 需要在使用时跟踪命中率
        }


class CachedRequestManager(RequestManager):
    """带缓存的请求管理器"""
    
    def __init__(self, config: ChromeMCPConfig, cache_ttl: int = 300):
        super().__init__(config)
        self.cache = RequestCache(ttl=cache_ttl)
        self.cache_hits = 0
        self.cache_misses = 0
    
    async def send_request(
        self, 
        method: str, 
        params: Optional[Dict[str, Any]] = None,
        timeout: Optional[float] = None,
        retry_count: Optional[int] = None,
        use_cache: bool = False
    ) -> Dict[str, Any]:
        """发送请求（支持缓存）"""
        params = params or {}
        
        # 检查缓存
        if use_cache and self._is_cacheable(method):
            cached_result = self.cache.get(method, params)
            if cached_result is not None:
                self.cache_hits += 1
                logger.debug(f"Cache hit for {method}")
                return cached_result
            else:
                self.cache_misses += 1
        
        # 发送请求
        result = await super().send_request(method, params, timeout, retry_count)
        
        # 缓存结果
        if use_cache and self._is_cacheable(method):
            self.cache.set(method, params, result)
        
        return result
    
    def _is_cacheable(self, method: str) -> bool:
        """判断方法是否可缓存"""
        # 只缓存读取操作，不缓存修改操作
        read_only_methods = {
            "chrome_get_element_text",
            "chrome_get_page_source", 
            "chrome_get_current_url",
            "chrome_is_element_visible",
            "list_tools"
        }
        return method in read_only_methods
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = self.cache_hits / total_requests if total_requests > 0 else 0.0
        
        cache_stats = self.cache.get_stats()
        cache_stats.update({
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate": hit_rate
        })
        
        return cache_stats