"""
MCP连接管理器
处理连接生命周期、自动重连和健康检查
"""

import asyncio
from typing import Optional, Dict, Any, Callable, Awaitable
from contextlib import asynccontextmanager
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ..config import ChromeMCPConfig
from .mcp_errors import MCPError, MCPConnectionError, MCPTimeoutError, MCPErrorHandler


class ConnectionManager:
    """连接管理器 - 处理连接生命周期和健康检查"""
    
    def __init__(self, config: ChromeMCPConfig):
        self.config = config
        self._connection_lock = asyncio.Lock()
        self._health_check_task: Optional[asyncio.Task] = None
        self._shutdown_event = asyncio.Event()
        self._connection_callbacks: Dict[str, Callable[[], Awaitable[bool]]] = {}
        self._disconnect_callbacks: Dict[str, Callable[[], Awaitable[None]]] = {}
        self._health_check_callbacks: Dict[str, Callable[[], Awaitable[bool]]] = {}
        
        # 连接状态跟踪
        self._connection_stats = {
            "total_connections": 0,
            "failed_connections": 0,
            "reconnections": 0,
            "last_connection_time": None,
            "last_health_check": None,
            "consecutive_failures": 0
        }
    
    def register_connection_callback(self, name: str, callback: Callable[[], Awaitable[bool]]):
        """注册连接回调函数"""
        self._connection_callbacks[name] = callback
    
    def register_disconnect_callback(self, name: str, callback: Callable[[], Awaitable[None]]):
        """注册断开连接回调函数"""
        self._disconnect_callbacks[name] = callback
    
    def register_health_check_callback(self, name: str, callback: Callable[[], Awaitable[bool]]):
        """注册健康检查回调函数"""
        self._health_check_callbacks[name] = callback
    
    async def ensure_connected(self) -> bool:
        """确保连接可用"""
        async with self._connection_lock:
            # 检查是否已连接
            if await self._check_all_connections():
                return True
            
            # 尝试连接
            logger.info("Connection not available, attempting to connect...")
            return await self._connect_all()
    
    async def _check_all_connections(self) -> bool:
        """检查所有注册的连接是否可用"""
        if not self._health_check_callbacks:
            return False
        
        try:
            for name, callback in self._health_check_callbacks.items():
                if not await callback():
                    logger.debug(f"Health check failed for {name}")
                    return False
            return True
        except Exception as e:
            logger.debug(f"Health check error: {e}")
            return False
    
    async def _connect_all(self) -> bool:
        """连接所有注册的连接"""
        self._connection_stats["total_connections"] += 1
        
        try:
            success_count = 0
            for name, callback in self._connection_callbacks.items():
                try:
                    if await callback():
                        success_count += 1
                        logger.debug(f"Successfully connected {name}")
                    else:
                        logger.warning(f"Failed to connect {name}")
                except Exception as e:
                    logger.error(f"Error connecting {name}: {e}")
            
            if success_count == len(self._connection_callbacks):
                self._connection_stats["consecutive_failures"] = 0
                self._connection_stats["last_connection_time"] = asyncio.get_event_loop().time()
                
                # 启动健康检查任务
                if not self._health_check_task or self._health_check_task.done():
                    self._health_check_task = asyncio.create_task(self._health_check_loop())
                
                return True
            else:
                self._connection_stats["failed_connections"] += 1
                self._connection_stats["consecutive_failures"] += 1
                return False
                
        except Exception as e:
            self._connection_stats["failed_connections"] += 1
            self._connection_stats["consecutive_failures"] += 1
            logger.error(f"Connection attempt failed: {e}")
            return False
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type((MCPConnectionError, MCPTimeoutError))
    )
    async def reconnect(self) -> bool:
        """重新连接（带重试机制）"""
        logger.info("Attempting to reconnect...")
        self._connection_stats["reconnections"] += 1
        
        try:
            # 先断开现有连接
            await self._disconnect_all()
            
            # 等待一段时间再重连
            await asyncio.sleep(self.config.retry_delay)
            
            # 尝试重新连接
            success = await self._connect_all()
            
            if success:
                logger.info("Reconnection successful")
                return True
            else:
                raise MCPConnectionError("Reconnection failed")
                
        except Exception as e:
            logger.error(f"Reconnection failed: {e}")
            raise
    
    async def _disconnect_all(self):
        """断开所有连接"""
        for name, callback in self._disconnect_callbacks.items():
            try:
                await callback()
                logger.debug(f"Disconnected {name}")
            except Exception as e:
                logger.debug(f"Error disconnecting {name}: {e}")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while not self._shutdown_event.is_set():
            try:
                await asyncio.sleep(self.config.health_check_interval)
                
                # 执行健康检查
                health_status = await self._check_all_connections()
                self._connection_stats["last_health_check"] = asyncio.get_event_loop().time()
                
                if not health_status:
                    logger.warning("Health check failed, attempting reconnection...")
                    try:
                        await self.reconnect()
                    except Exception as e:
                        logger.error(f"Auto-reconnection failed: {e}")
                        # 继续循环，下次再试
                        
            except asyncio.CancelledError:
                logger.debug("Health check loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
    
    async def health_check(self) -> bool:
        """手动健康检查"""
        try:
            return await self._check_all_connections()
        except Exception as e:
            logger.debug(f"Health check failed: {e}")
            return False
    
    @asynccontextmanager
    async def ensure_connection(self):
        """确保连接可用的上下文管理器"""
        if not await self.ensure_connected():
            raise MCPConnectionError("Failed to establish connection")
        
        try:
            yield
        except Exception as e:
            # 如果是连接相关错误，标记需要重连
            if isinstance(e, (MCPConnectionError, MCPTimeoutError)):
                logger.warning(f"Connection error detected: {e}")
                # 不在这里重连，让健康检查循环处理
            raise
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        current_time = asyncio.get_event_loop().time()
        
        stats = self._connection_stats.copy()
        stats.update({
            "registered_connections": len(self._connection_callbacks),
            "health_check_enabled": self._health_check_task is not None and not self._health_check_task.done(),
            "uptime_seconds": current_time - stats["last_connection_time"] if stats["last_connection_time"] else 0,
            "time_since_last_health_check": current_time - stats["last_health_check"] if stats["last_health_check"] else None
        })
        
        return stats
    
    async def shutdown(self):
        """关闭连接管理器"""
        logger.info("Shutting down connection manager...")
        
        # 停止健康检查循环
        self._shutdown_event.set()
        
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 断开所有连接
        await self._disconnect_all()
        
        logger.info("Connection manager shutdown complete")


class ConnectionPool:
    """连接池管理器（可选功能）"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self._semaphore = asyncio.Semaphore(max_connections)
        self._active_connections = 0
        self._connection_lock = asyncio.Lock()
    
    @asynccontextmanager
    async def acquire_connection(self):
        """获取连接槽位"""
        async with self._semaphore:
            async with self._connection_lock:
                self._active_connections += 1
            
            try:
                yield
            finally:
                async with self._connection_lock:
                    self._active_connections -= 1
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        return {
            "max_connections": self.max_connections,
            "active_connections": self._active_connections,
            "available_connections": self.max_connections - self._active_connections
        }


class ConnectionHealthMonitor:
    """连接健康监控器"""
    
    def __init__(self, config: ChromeMCPConfig):
        self.config = config
        self._metrics = {
            "total_health_checks": 0,
            "failed_health_checks": 0,
            "average_response_time": 0.0,
            "response_times": []
        }
    
    async def perform_health_check(self, health_check_func: Callable[[], Awaitable[bool]]) -> bool:
        """执行健康检查并记录指标"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            self._metrics["total_health_checks"] += 1
            result = await asyncio.wait_for(
                health_check_func(),
                timeout=5.0  # 健康检查超时时间
            )
            
            # 记录响应时间
            response_time = asyncio.get_event_loop().time() - start_time
            self._metrics["response_times"].append(response_time)
            
            # 保持最近100次的响应时间
            if len(self._metrics["response_times"]) > 100:
                self._metrics["response_times"] = self._metrics["response_times"][-100:]
            
            # 计算平均响应时间
            self._metrics["average_response_time"] = sum(self._metrics["response_times"]) / len(self._metrics["response_times"])
            
            if not result:
                self._metrics["failed_health_checks"] += 1
            
            return result
            
        except asyncio.TimeoutError:
            self._metrics["failed_health_checks"] += 1
            logger.warning("Health check timeout")
            return False
        except Exception as e:
            self._metrics["failed_health_checks"] += 1
            logger.debug(f"Health check error: {e}")
            return False
    
    def get_health_metrics(self) -> Dict[str, Any]:
        """获取健康检查指标"""
        total_checks = self._metrics["total_health_checks"]
        failed_checks = self._metrics["failed_health_checks"]
        
        return {
            "total_health_checks": total_checks,
            "failed_health_checks": failed_checks,
            "success_rate": (total_checks - failed_checks) / total_checks if total_checks > 0 else 0.0,
            "average_response_time_ms": self._metrics["average_response_time"] * 1000,
            "recent_response_times": self._metrics["response_times"][-10:]  # 最近10次
        }