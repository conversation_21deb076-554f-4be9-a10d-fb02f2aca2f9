"""
Chrome MCP工具调用封装
提供Chrome浏览器操作的高级接口
"""

from typing import Dict, Any, Optional, List
from loguru import logger

from .mcp_wrapper import MC<PERSON>lientWrapper
from ..config import MCPResponse, MCPError, MCPErrorType


class ChromeToolsClient:
    """Chrome MCP工具客户端"""
    
    def __init__(self, mcp_wrapper: MCPClientWrapper):
        self.mcp = mcp_wrapper
    
    async def navigate_to(self, url: str) -> MCPResponse:
        """导航到指定URL"""
        logger.info(f"Navigating to: {url}")
        
        return await self.mcp.call_tool("navigate", {
            "url": url
        })
    
    async def wait_for_load(self, timeout: int = 10) -> MCPResponse:
        """等待页面加载完成"""
        return await self.mcp.call_tool("wait_for_load", {
            "timeout": timeout
        })
    
    async def find_element(self, selector: str) -> MCPResponse:
        """查找页面元素"""
        return await self.mcp.call_tool("find_element", {
            "selector": selector
        })
    
    async def find_elements(self, selector: str) -> MCPResponse:
        """查找多个页面元素"""
        return await self.mcp.call_tool("find_elements", {
            "selector": selector
        })
    
    async def get_element_text(self, selector: str) -> str:
        """获取元素文本"""
        result = await self.mcp.call_tool("get_element_text", {
            "selector": selector
        })
        
        if result.success and result.result:
            return result.result.get("text", "")
        return ""
    
    async def click_element(self, selector: str) -> MCPResponse:
        """点击元素"""
        logger.info(f"Clicking element: {selector}")
        
        return await self.mcp.call_tool("click_element", {
            "selector": selector
        })
    
    async def input_text(self, selector: str, text: str, clear: bool = True) -> MCPResponse:
        """输入文本"""
        logger.info(f"Inputting text to {selector}: {text}")
        
        return await self.mcp.call_tool("input_text", {
            "selector": selector,
            "text": text,
            "clear": clear
        })
    
    async def select_option(self, selector: str, value: str) -> MCPResponse:
        """选择下拉框选项"""
        logger.info(f"Selecting option {value} from {selector}")
        
        return await self.mcp.call_tool("select_option", {
            "selector": selector,
            "value": value
        })
    
    async def upload_file(self, selector: str, file_path: str) -> MCPResponse:
        """上传文件"""
        logger.info(f"Uploading file {file_path} to {selector}")
        
        return await self.mcp.call_tool("upload_file", {
            "selector": selector,
            "file_path": file_path
        })
    
    async def take_screenshot(self, file_path: Optional[str] = None) -> MCPResponse:
        """截图"""
        params = {}
        if file_path:
            params["file_path"] = file_path
            
        return await self.mcp.call_tool("take_screenshot", params)
    
    async def get_page_source(self) -> str:
        """获取页面源码"""
        result = await self.mcp.call_tool("get_page_source", {})
        
        if result.success and result.result:
            return result.result.get("source", "")
        return ""
    
    async def execute_script(self, script: str) -> MCPResponse:
        """执行JavaScript脚本"""
        return await self.mcp.call_tool("execute_script", {
            "script": script
        })
    
    async def get_current_url(self) -> str:
        """获取当前URL"""
        result = await self.mcp.call_tool("get_current_url", {})
        
        if result.success and result.result:
            return result.result.get("url", "")
        return ""
    
    async def wait_for_element(self, selector: str, timeout: int = 10) -> MCPResponse:
        """等待元素出现"""
        return await self.mcp.call_tool("wait_for_element", {
            "selector": selector,
            "timeout": timeout
        })
    
    async def is_element_visible(self, selector: str) -> bool:
        """检查元素是否可见"""
        result = await self.mcp.call_tool("is_element_visible", {
            "selector": selector
        })
        
        if result.success and result.result:
            return result.result.get("visible", False)
        return False
    
    async def scroll_to_element(self, selector: str) -> MCPResponse:
        """滚动到元素"""
        return await self.mcp.call_tool("scroll_to_element", {
            "selector": selector
        })
    
    async def get_element_attribute(self, selector: str, attribute: str) -> str:
        """获取元素属性"""
        result = await self.mcp.call_tool("get_element_attribute", {
            "selector": selector,
            "attribute": attribute
        })
        
        if result.success and result.result:
            return result.result.get("value", "")
        return ""
    
    async def hover_element(self, selector: str) -> MCPResponse:
        """悬停在元素上"""
        return await self.mcp.call_tool("hover_element", {
            "selector": selector
        })
    
    async def double_click_element(self, selector: str) -> MCPResponse:
        """双击元素"""
        return await self.mcp.call_tool("double_click_element", {
            "selector": selector
        })
    
    async def right_click_element(self, selector: str) -> MCPResponse:
        """右键点击元素"""
        return await self.mcp.call_tool("right_click_element", {
            "selector": selector
        })
    
    async def clear_input(self, selector: str) -> MCPResponse:
        """清空输入框"""
        return await self.mcp.call_tool("clear_input", {
            "selector": selector
        })
    
    async def press_key(self, key: str) -> MCPResponse:
        """按键"""
        return await self.mcp.call_tool("press_key", {
            "key": key
        })
    
    async def get_page_title(self) -> str:
        """获取页面标题"""
        result = await self.mcp.call_tool("get_page_title", {})
        
        if result.success and result.result:
            return result.result.get("title", "")
        return ""