import aiohttp
import json
from typing import List, Dict, Any, Optional
from loguru import logger
from ..config import SiliconFlowConfig

class SiliconFlowClient:
    def __init__(self, config: SiliconFlowConfig):
        self.config = config
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def chat_completion(self, messages: List[Dict[str, str]], 
                            temperature: Optional[float] = None,
                            max_tokens: Optional[int] = None) -> str:
        """调用聊天完成API"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.config.model,
            "messages": messages,
            "temperature": temperature or self.config.temperature,
            "max_tokens": max_tokens or self.config.max_tokens,
            "stream": False
        }
        
        try:
            async with self.session.post(
                f"{self.config.base_url}/chat/completions",
                headers=headers,
                json=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    error_text = await response.text()
                    logger.error(f"SiliconFlow API error: {response.status} - {error_text}")
                    raise Exception(f"API request failed: {response.status}")
        
        except Exception as e:
            logger.error(f"Error calling SiliconFlow API: {e}")
            raise
    
    async def analyze_intent(self, user_input: str) -> Dict[str, Any]:
        """分析用户意图"""
        prompt = f"""
        分析以下用户输入的意图，并提取相关信息：

        用户输入：{user_input}

        请返回JSON格式的结果，包含：
        1. intent: 意图类型（如：expense_report, leave_request等）
        2. entities: 提取的实体信息
        3. confidence: 置信度(0-1)
        4. missing_info: 缺失的必要信息

        示例：
        {{
            "intent": "expense_report",
            "entities": {{
                "employee_name": "张三",
                "amount": "500.00",
                "description": "差旅费"
            }},
            "confidence": 0.9,
            "missing_info": ["employee_id", "receipt"]
        }}
        """
        
        messages = [
            {"role": "system", "content": "你是一个智能助手，擅长分析用户意图和提取信息。"},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.chat_completion(messages)
        
        try:
            # 尝试从响应中提取JSON
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                return {"intent": "unknown", "entities": {}, "confidence": 0.0, "missing_info": []}
        except json.JSONDecodeError:
            logger.warning("Failed to parse intent analysis result as JSON")
            return {"intent": "unknown", "entities": {}, "confidence": 0.0, "missing_info": []}
    
    async def extract_form_data(self, page_content: str, form_config: Dict[str, Any]) -> Dict[str, str]:
        """从页面内容中提取表单字段信息"""
        prompt = f"""
        分析以下HTML页面内容，根据表单配置提取可用的表单字段信息：

        页面内容：
        {page_content[:2000]}...

        表单配置：
        {json.dumps(form_config, indent=2, ensure_ascii=False)}

        请返回JSON格式的结果，包含每个字段的以下信息：
        - selector: CSS选择器
        - field_type: 字段类型
        - current_value: 当前值（如果有）
        - options: 选项列表（对于select字段）
        - is_required: 是否必填
        - is_visible: 是否可见

        示例：
        {{
            "employee_name": {{
                "selector": "input[name='employeeName']",
                "field_type": "text",
                "current_value": "",
                "is_required": true,
                "is_visible": true
            }}
        }}
        """
        
        messages = [
            {"role": "system", "content": "你是一个网页分析专家，擅长解析HTML表单结构。"},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.chat_completion(messages)
        
        try:
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                return {}
        except json.JSONDecodeError:
            logger.warning("Failed to parse form data extraction result as JSON")
            return {}
    
    async def generate_response(self, user_input: str, context: Dict[str, Any]) -> str:
        """生成回复"""
        system_prompt = """
        你是一个智能表单填写助手。你的任务是：
        1. 理解用户的需求
        2. 引导用户提供必要信息
        3. 协助完成表单填写
        4. 在遇到问题时寻求用户帮助

        请保持对话自然、友好，并在需要时主动询问缺失的信息。
        """
        
        context_str = json.dumps(context, indent=2, ensure_ascii=False)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"用户输入：{user_input}\n当前上下文：{context_str}"}
        ]
        
        return await self.chat_completion(messages)
    
    async def plan_next_action(self, current_state: Dict[str, Any]) -> Dict[str, Any]:
        """规划下一步动作"""
        prompt = f"""
        根据当前状态，规划下一步的动作：

        当前状态：
        {json.dumps(current_state, indent=2, ensure_ascii=False)}

        请返回JSON格式的动作计划：
        {{
            "action": "action_type",  // navigate, fill_field, click, submit, ask_user
            "target": "target_selector_or_url",
            "value": "value_to_input",
            "reason": "执行此动作的原因",
            "next_steps": ["后续步骤1", "后续步骤2"]
        }}
        """
        
        messages = [
            {"role": "system", "content": "你是一个动作规划专家，擅长制定表单填写的执行计划。"},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.chat_completion(messages)
        
        try:
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                return {"action": "ask_user", "reason": "无法确定下一步动作"}
        except json.JSONDecodeError:
            logger.warning("Failed to parse action plan as JSON")
            return {"action": "ask_user", "reason": "规划解析失败"}