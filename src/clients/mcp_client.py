import asyncio
from typing import Dict, Any, Optional, List
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential

from ..config import ChromeMCPConfig
from .mcp_transport import MCPTransportManager
from .connection_manager import ConnectionManager
from .request_manager import Request<PERSON>ana<PERSON>, CachedRequestManager
from .mcp_errors import MCPError, MCPConnectionError, MCPRequestError, MCPTimeoutError, MCPErrorHandler

class ChromeMCPClient:
    """Chrome MCP客户端 - 使用RequestManager、ConnectionManager和StreamableHTTP传输"""
    
    def __init__(self, config: ChromeMCPConfig):
        self.config = config
        self.transport = MCPTransportManager(config)
        self.connection_manager = ConnectionManager(config)
        
        # 创建请求管理器（支持缓存）
        self.request_manager = CachedRequestManager(config, cache_ttl=300)
        
        # 注册连接管理回调
        self.connection_manager.register_connection_callback("transport", self.transport.connect)
        self.connection_manager.register_disconnect_callback("transport", self.transport.disconnect)
        self.connection_manager.register_health_check_callback("transport", self.transport.health_check)
        
        # 注册请求处理回调
        self.request_manager.register_request_handler("transport", self._handle_transport_request)
        
    async def connect(self) -> bool:
        """连接到Chrome MCP Server"""
        try:
            success = await self.connection_manager.ensure_connected()
            if success:
                logger.info(f"Connected to Chrome MCP Server at {self.config.mcp_url}")
            return success
        except Exception as e:
            error = MCPErrorHandler.handle_connection_error(e, self.config.mcp_url)
            MCPErrorHandler.log_error(error, "connect")
            return False
    
    async def disconnect(self):
        """断开连接"""
        await self.connection_manager.shutdown()
        logger.info("Disconnected from Chrome MCP Server")
    
    async def _handle_transport_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理传输请求"""
        async with self.connection_manager.ensure_connection():
            return await self.transport.call_tool(method, params)
    
    async def send_request(
        self, 
        method: str, 
        params: Optional[Dict[str, Any]] = None,
        timeout: Optional[float] = None,
        use_cache: bool = False
    ) -> Dict[str, Any]:
        """发送MCP请求（统一入口）"""
        return await self.request_manager.send_request(
            method=method,
            params=params,
            timeout=timeout,
            use_cache=use_cache
        )
    
    @property
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.transport.is_connected
    
    async def navigate_to(self, url: str) -> Dict[str, Any]:
        """导航到指定URL"""
        logger.info(f"Navigating to: {url}")
        try:
            return await self.send_request("chrome_navigate", {"url": url})
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "navigate_to", {"url": url})
            MCPErrorHandler.log_error(error, "navigate_to")
            raise error
    
    async def wait_for_load(self, timeout: int = 10) -> Dict[str, Any]:
        """等待页面加载完成"""
        try:
            return await self.send_request("chrome_wait_for_load", {"timeout": timeout})
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "wait_for_load", {"timeout": timeout})
            MCPErrorHandler.log_error(error, "wait_for_load")
            raise error
    
    async def find_element(self, selector: str) -> Dict[str, Any]:
        """查找页面元素"""
        try:
            return await self.send_request("chrome_find_element", {"selector": selector})
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "find_element", {"selector": selector})
            MCPErrorHandler.log_error(error, "find_element")
            raise error
    
    async def find_elements(self, selector: str) -> Dict[str, Any]:
        """查找多个页面元素"""
        try:
            return await self.send_request("chrome_find_elements", {"selector": selector})
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "find_elements", {"selector": selector})
            MCPErrorHandler.log_error(error, "find_elements")
            raise error
    
    async def get_element_text(self, selector: str) -> str:
        """获取元素文本"""
        try:
            result = await self.send_request("chrome_get_element_text", {"selector": selector}, use_cache=True)
            return result.get("result", {}).get("text", "")
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "get_element_text", {"selector": selector})
            MCPErrorHandler.log_error(error, "get_element_text")
            raise error
    
    async def click_element(self, selector: str) -> Dict[str, Any]:
        """点击元素"""
        try:
            return await self.send_request("chrome_click", {"selector": selector})
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "click_element", {"selector": selector})
            MCPErrorHandler.log_error(error, "click_element")
            raise error
    
    async def input_text(self, selector: str, text: str, clear: bool = True) -> Dict[str, Any]:
        """输入文本"""
        try:
            return await self.send_request("chrome_input_text", {
                "selector": selector, 
                "text": text, 
                "clear": clear
            })
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "input_text", {"selector": selector, "text": text})
            MCPErrorHandler.log_error(error, "input_text")
            raise error
    
    async def select_option(self, selector: str, value: str) -> Dict[str, Any]:
        """选择下拉框选项"""
        try:
            return await self.send_request("chrome_select_option", {
                "selector": selector, 
                "value": value
            })
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "select_option", {"selector": selector, "value": value})
            MCPErrorHandler.log_error(error, "select_option")
            raise error
    
    async def upload_file(self, selector: str, file_path: str) -> Dict[str, Any]:
        """上传文件"""
        try:
            return await self.send_request("chrome_upload_file", {
                "selector": selector, 
                "file_path": file_path
            })
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "upload_file", {"selector": selector, "file_path": file_path})
            MCPErrorHandler.log_error(error, "upload_file")
            raise error
    
    async def take_screenshot(self, file_path: str = None) -> Dict[str, Any]:
        """截图"""
        try:
            params = {}
            if file_path:
                params["file_path"] = file_path
            return await self.send_request("chrome_screenshot", params)
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "take_screenshot", {"file_path": file_path})
            MCPErrorHandler.log_error(error, "take_screenshot")
            raise error
    
    async def get_page_source(self) -> str:
        """获取页面源码"""
        try:
            result = await self.send_request("chrome_get_page_source", {}, use_cache=True)
            return result.get("result", {}).get("source", "")
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "get_page_source", {})
            MCPErrorHandler.log_error(error, "get_page_source")
            raise error
    
    async def execute_script(self, script: str) -> Dict[str, Any]:
        """执行JavaScript脚本"""
        try:
            return await self.send_request("chrome_execute_script", {"script": script})
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "execute_script", {"script": script})
            MCPErrorHandler.log_error(error, "execute_script")
            raise error
    
    async def get_current_url(self) -> str:
        """获取当前URL"""
        try:
            result = await self.send_request("chrome_get_current_url", {}, use_cache=True)
            return result.get("result", {}).get("url", "")
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "get_current_url", {})
            MCPErrorHandler.log_error(error, "get_current_url")
            raise error
    
    async def wait_for_element(self, selector: str, timeout: int = 10) -> Dict[str, Any]:
        """等待元素出现"""
        try:
            return await self.send_request("chrome_wait_for_element", {
                "selector": selector, 
                "timeout": timeout
            })
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "wait_for_element", {"selector": selector, "timeout": timeout})
            MCPErrorHandler.log_error(error, "wait_for_element")
            raise error
    
    async def is_element_visible(self, selector: str) -> bool:
        """检查元素是否可见"""
        try:
            result = await self.send_request("chrome_is_element_visible", {"selector": selector}, use_cache=True)
            return result.get("result", {}).get("visible", False)
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "is_element_visible", {"selector": selector})
            MCPErrorHandler.log_error(error, "is_element_visible")
            raise error
    
    # 连接状态和管理方法
    async def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态"""
        connection_stats = await self.connection_manager.get_connection_stats()
        
        # 只在连接时获取传输信息
        transport_info = {}
        if self.is_connected:
            try:
                transport_info = await self.transport.get_server_info()
            except Exception as e:
                logger.debug(f"Failed to get transport info: {e}")
        
        return {
            "connected": self.is_connected,
            "server_url": self.config.mcp_url,
            "transport_type": self.config.transport_type,
            "health_check_interval": self.config.health_check_interval,
            "connection_stats": connection_stats,
            "transport_info": transport_info
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        return await self.connection_manager.health_check()
    
    async def reconnect(self) -> bool:
        """手动重连"""
        try:
            return await self.connection_manager.reconnect()
        except Exception as e:
            logger.error(f"Manual reconnection failed: {e}")
            return False
    
    async def list_available_tools(self) -> List[Dict[str, Any]]:
        """列出可用的Chrome工具"""
        try:
            async with self.connection_manager.ensure_connection():
                return await self.transport.list_tools()
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "list_available_tools", {})
            MCPErrorHandler.log_error(error, "list_available_tools")
            raise error
    
    async def get_server_info(self) -> Dict[str, Any]:
        """获取服务器信息"""
        try:
            async with self.connection_manager.ensure_connection():
                return await self.transport.get_server_info()
        except MCPError:
            raise
        except Exception as e:
            error = MCPErrorHandler.handle_request_error(e, "get_server_info", {})
            MCPErrorHandler.log_error(error, "get_server_info")
            raise error
    
    # 请求管理和性能监控方法
    async def get_request_metrics(self) -> Dict[str, Any]:
        """获取请求指标"""
        return self.request_manager.get_metrics()
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        if hasattr(self.request_manager, 'get_cache_stats'):
            return self.request_manager.get_cache_stats()
        return {}
    
    def reset_request_metrics(self):
        """重置请求指标"""
        self.request_manager.reset_metrics()
    
    def clear_cache(self):
        """清空请求缓存"""
        if hasattr(self.request_manager, 'cache'):
            self.request_manager.cache.clear()