import aiohttp
import json
import base64
from typing import Dict, Any, Optional
from loguru import logger
from ..config import ZhipuAIConfig
from PIL import Image
import io

class ZhipuOCRClient:
    """智谱AI GLM-4.1V-Thinking OCR客户端"""
    
    def __init__(self, config: ZhipuAIConfig):
        self.config = config
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def _encode_image_to_base64(self, image_path: str) -> str:
        """将图像文件编码为base64"""
        try:
            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                return encoded_string
        except Exception as e:
            logger.error(f"Error encoding image to base64: {e}")
            raise
    
    def _optimize_image_for_ocr(self, image_path: str) -> str:
        """优化图像以提高OCR效果"""
        try:
            # 打开图像
            with Image.open(image_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 如果图像太大，调整大小以提高处理速度
                max_size = 2048
                if max(img.size) > max_size:
                    ratio = max_size / max(img.size)
                    new_size = tuple(int(dim * ratio) for dim in img.size)
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # 保存到内存中的字节流
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=95)
                buffer.seek(0)
                
                # 编码为base64
                encoded_string = base64.b64encode(buffer.getvalue()).decode('utf-8')
                return encoded_string
        
        except Exception as e:
            logger.error(f"Error optimizing image: {e}")
            # 如果优化失败，使用原始图像
            return self._encode_image_to_base64(image_path)
    
    async def extract_text_from_image(self, image_path: str) -> str:
        """从图像中提取文本"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            # 编码图像
            base64_image = self._optimize_image_for_ocr(image_path)
            
            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            # 使用GLM-4.1V-Thinking进行OCR
            data = {
                "model": self.config.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "请仔细识别这张图片中的所有文字内容，包括中文和英文。请按照原始布局尽可能准确地输出所有文字，保持原有的格式和结构。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens
            }
            
            # 发送请求
            async with self.session.post(
                f"{self.config.base_url}/chat/completions",
                headers=headers,
                json=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    # 提取文本内容
                    if "choices" in result and len(result["choices"]) > 0:
                        content = result["choices"][0]["message"]["content"]
                        logger.info(f"ZhipuAI OCR extracted {len(content)} characters from {image_path}")
                        return content
                    else:
                        logger.warning("No content found in ZhipuAI response")
                        return ""
                else:
                    error_text = await response.text()
                    logger.error(f"ZhipuAI API error: {response.status} - {error_text}")
                    raise Exception(f"API request failed: {response.status}")
        
        except Exception as e:
            logger.error(f"Error in ZhipuAI OCR: {e}")
            raise
    
    async def extract_structured_data(self, image_path: str, data_type: str = "receipt") -> Dict[str, Any]:
        """从图像中提取结构化数据"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            # 编码图像
            base64_image = self._optimize_image_for_ocr(image_path)
            
            # 根据数据类型构建不同的提示词
            if data_type == "receipt":
                prompt = """
请仔细分析这张发票/收据图片，提取以下信息并以JSON格式返回：
1. 金额 (amount): 总金额数字
2. 日期 (date): 交易日期
3. 商户名称 (merchant): 商家或机构名称
4. 类别 (category): 费用类别（如餐饮费、交通费、住宿费、办公费等）
5. 发票号码 (invoice_number): 发票或收据编号

请只返回JSON格式的结果，格式如下：
{
    "amount": "金额",
    "date": "日期",
    "merchant": "商户名称", 
    "category": "费用类别",
    "invoice_number": "发票号码"
}
"""
            elif data_type == "id_card":
                prompt = """
请仔细分析这张身份证图片，提取以下信息并以JSON格式返回：
1. 姓名 (name): 持证人姓名
2. 身份证号码 (id_number): 18位身份证号码
3. 性别 (gender): 性别
4. 出生日期 (birth_date): 出生日期
5. 地址 (address): 住址信息

请只返回JSON格式的结果，格式如下：
{
    "name": "姓名",
    "id_number": "身份证号码",
    "gender": "性别",
    "birth_date": "出生日期",
    "address": "地址"
}
"""
            else:
                # 通用文本提取
                return {"raw_text": await self.extract_text_from_image(image_path)}
            
            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.config.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens
            }
            
            # 发送请求
            async with self.session.post(
                f"{self.config.base_url}/chat/completions",
                headers=headers,
                json=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    if "choices" in result and len(result["choices"]) > 0:
                        content = result["choices"][0]["message"]["content"]
                        
                        # 尝试解析JSON
                        try:
                            # 提取JSON部分
                            import re
                            json_match = re.search(r'\{.*\}', content, re.DOTALL)
                            if json_match:
                                structured_data = json.loads(json_match.group())
                                logger.info(f"ZhipuAI extracted structured data: {structured_data}")
                                return structured_data
                            else:
                                logger.warning("No JSON found in ZhipuAI response")
                                return {"raw_text": content}
                        
                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to parse JSON from ZhipuAI response: {e}")
                            return {"raw_text": content}
                    else:
                        logger.warning("No content found in ZhipuAI response")
                        return {}
                else:
                    error_text = await response.text()
                    logger.error(f"ZhipuAI API error: {response.status} - {error_text}")
                    raise Exception(f"API request failed: {response.status}")
        
        except Exception as e:
            logger.error(f"Error in ZhipuAI structured OCR: {e}")
            raise
    
    async def extract_text_from_base64(self, base64_data: str) -> str:
        """从base64图像数据中提取文本"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            # 清理base64数据
            if ',' in base64_data:
                base64_data = base64_data.split(',')[1]
            
            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.config.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "请仔细识别这张图片中的所有文字内容，包括中文和英文。请按照原始布局尽可能准确地输出所有文字。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_data}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens
            }
            
            # 发送请求
            async with self.session.post(
                f"{self.config.base_url}/chat/completions",
                headers=headers,
                json=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    if "choices" in result and len(result["choices"]) > 0:
                        content = result["choices"][0]["message"]["content"]
                        logger.info(f"ZhipuAI OCR extracted {len(content)} characters from base64 image")
                        return content
                    else:
                        logger.warning("No content found in ZhipuAI response")
                        return ""
                else:
                    error_text = await response.text()
                    logger.error(f"ZhipuAI API error: {response.status} - {error_text}")
                    raise Exception(f"API request failed: {response.status}")
        
        except Exception as e:
            logger.error(f"Error in ZhipuAI base64 OCR: {e}")
            raise