"""
MCP客户端包装器
使用FastMCP和StreamableHTTPTransport实现MCP连接
"""

import asyncio
from typing import Dict, Any, Optional, List
from loguru import logger
from contextlib import asynccontextmanager

from mcp.client.streamable_http import StreamableHTTPTransport
from mcp.client.session import ClientSession
from mcp import types

from ..config import ChromeMCPConfig, MCPResponse, MCPError, MCPErrorType


class MCPClientWrapper:
    """MCP客户端包装器类"""
    
    def __init__(self, config: ChromeMCPConfig):
        self.config = config
        self.transport: Optional[StreamableHTTPTransport] = None
        self.session: Optional[ClientSession] = None
        self._connected = False
        self._connection_lock = asyncio.Lock()
        
    async def connect(self) -> bool:
        """连接到MCP服务器"""
        async with self._connection_lock:
            if self._connected:
                return True
                
            try:
                # 构建MCP服务器URL
                mcp_url = f"{self.config.base_url}/mcp"
                
                # 创建StreamableHTTPTransport
                self.transport = StreamableHTTPTransport(
                    url=mcp_url,
                    timeout=self.config.timeout,
                    sse_read_timeout=self.config.sse_read_timeout
                )
                
                # 创建客户端会话
                read_stream, write_stream = self.transport.connect()
                self.session = ClientSession(read_stream, write_stream)
                
                # 初始化会话
                await self.session.initialize()
                
                self._connected = True
                logger.info(f"Successfully connected to MCP server at {mcp_url}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to connect to MCP server: {e}")
                await self._cleanup()
                return False
    
    async def disconnect(self):
        """断开MCP连接"""
        async with self._connection_lock:
            await self._cleanup()
            logger.info("Disconnected from MCP server")
    
    async def _cleanup(self):
        """清理连接资源"""
        self._connected = False
        
        if self.session:
            try:
                await self.session.close()
            except Exception as e:
                logger.warning(f"Error closing session: {e}")
            finally:
                self.session = None
        
        if self.transport:
            try:
                await self.transport.close()
            except Exception as e:
                logger.warning(f"Error closing transport: {e}")
            finally:
                self.transport = None
    
    @property
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected and self.session is not None
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> MCPResponse:
        """调用MCP工具"""
        if not self.is_connected:
            return MCPResponse.error_response(
                MCPError(
                    code=-1,
                    message="Not connected to MCP server",
                    error_type=MCPErrorType.CONNECTION_ERROR
                )
            )
        
        try:
            # 调用工具
            result = await self.session.call_tool(tool_name, arguments)
            
            if self.config.debug_mode:
                logger.debug(f"Tool call result: {tool_name} -> {result}")
            
            # 处理结果
            if result.isError:
                return MCPResponse.error_response(
                    MCPError(
                        code=result.content[0].error.code if result.content and hasattr(result.content[0], 'error') else -1,
                        message=result.content[0].error.message if result.content and hasattr(result.content[0], 'error') else "Tool call failed",
                        error_type=MCPErrorType.TOOL_NOT_FOUND
                    )
                )
            
            # 提取结果内容
            content = {}
            if result.content:
                for item in result.content:
                    if hasattr(item, 'text'):
                        content['text'] = item.text
                    elif hasattr(item, 'data'):
                        content['data'] = item.data
            
            return MCPResponse.success_response(content)
            
        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}")
            return MCPResponse.error_response(
                MCPError(
                    code=-1,
                    message=str(e),
                    error_type=MCPErrorType.SERVER_ERROR
                )
            )
    
    async def list_tools(self) -> MCPResponse:
        """列出可用的工具"""
        if not self.is_connected:
            return MCPResponse.error_response(
                MCPError(
                    code=-1,
                    message="Not connected to MCP server",
                    error_type=MCPErrorType.CONNECTION_ERROR
                )
            )
        
        try:
            tools = await self.session.list_tools()
            
            tools_list = []
            for tool in tools.tools:
                tools_list.append({
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema
                })
            
            return MCPResponse.success_response({"tools": tools_list})
            
        except Exception as e:
            logger.error(f"Error listing tools: {e}")
            return MCPResponse.error_response(
                MCPError(
                    code=-1,
                    message=str(e),
                    error_type=MCPErrorType.SERVER_ERROR
                )
            )
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.is_connected:
                return False
            
            # 尝试列出工具作为健康检查
            result = await self.list_tools()
            return result.success
            
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False
    
    @asynccontextmanager
    async def ensure_connection(self):
        """确保连接的上下文管理器"""
        if not self.is_connected:
            connected = await self.connect()
            if not connected:
                raise Exception("Failed to establish MCP connection")
        
        try:
            yield self
        finally:
            # 可以选择在这里断开连接，或保持连接以供重用
            pass