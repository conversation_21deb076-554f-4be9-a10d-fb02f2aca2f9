import os
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from dotenv import load_dotenv
from enum import Enum

load_dotenv()

class SiliconFlowConfig(BaseModel):
    api_key: str = os.getenv("SILICONFLOW_API_KEY", "")
    base_url: str = "https://api.siliconflow.cn/v1"
    model: str = os.getenv("SILICONFLOW_MODEL_NAME", "Qwen/Qwen3-8B")
    max_tokens: int = 4000
    temperature: float = 0.1

class ZhipuAIConfig(BaseModel):
    api_key: str = os.getenv("ZHIPU_API_KEY", "")
    base_url: str = "https://open.bigmodel.cn/api/paas/v4"
    model: str = os.getenv("ZHIPU_MODEL_NAME", "GLM-4.1V-Thinking-Flash")
    max_tokens: int = 4000
    temperature: float = 0.1

class ChromeMCPConfig(BaseModel):
    host: str = os.getenv("CHROME_MCP_HOST", "127.0.0.1")
    port: int = int(os.getenv("CHROME_MCP_PORT", "12306"))
    protocol: str = os.getenv("CHROME_MCP_PROTOCOL", "http")  # http, https
    timeout: int = int(os.getenv("CHROME_MCP_TIMEOUT", "30"))
    sse_read_timeout: int = int(os.getenv("CHROME_MCP_SSE_READ_TIMEOUT", "300"))
    max_retries: int = int(os.getenv("CHROME_MCP_MAX_RETRIES", "3"))
    retry_delay: float = float(os.getenv("CHROME_MCP_RETRY_DELAY", "2.0"))
    health_check_interval: int = int(os.getenv("CHROME_MCP_HEALTH_CHECK_INTERVAL", "60"))
    debug_mode: bool = os.getenv("CHROME_MCP_DEBUG", "false").lower() == "true"
    
    # 新增配置项
    transport_type: str = os.getenv("CHROME_MCP_TRANSPORT", "streamable_http")  # streamable_http, websocket
    use_https: bool = os.getenv("CHROME_MCP_USE_HTTPS", "false").lower() == "true"
    auth_token: Optional[str] = os.getenv("CHROME_MCP_AUTH_TOKEN")
    connection_pool_size: int = int(os.getenv("CHROME_MCP_POOL_SIZE", "10"))
    
    @property
    def base_url(self) -> str:
        """构建基础URL"""
        return f"{self.protocol}://{self.host}:{self.port}"
    
    @property
    def mcp_url(self) -> str:
        """构建MCP服务URL"""
        return f"{self.base_url}/mcp"

class MCPErrorType(str, Enum):
    """MCP错误类型枚举"""
    CONNECTION_ERROR = "connection_error"
    TIMEOUT_ERROR = "timeout_error"
    AUTHENTICATION_ERROR = "authentication_error"
    TOOL_NOT_FOUND = "tool_not_found"
    INVALID_PARAMS = "invalid_params"
    SERVER_ERROR = "server_error"
    UNKNOWN_ERROR = "unknown_error"

class MCPError(BaseModel):
    """MCP错误模型"""
    code: int
    message: str
    data: Optional[Dict[str, Any]] = None
    error_type: MCPErrorType = MCPErrorType.UNKNOWN_ERROR

class MCPResponse(BaseModel):
    """MCP响应基础模型"""
    success: bool = True
    result: Optional[Dict[str, Any]] = None
    error: Optional[MCPError] = None
    request_id: Optional[str] = None
    
    @classmethod
    def success_response(cls, result: Dict[str, Any], request_id: Optional[str] = None) -> "MCPResponse":
        """创建成功响应"""
        return cls(success=True, result=result, request_id=request_id)
    
    @classmethod
    def error_response(cls, error: MCPError, request_id: Optional[str] = None) -> "MCPResponse":
        """创建错误响应"""
        return cls(success=False, error=error, request_id=request_id)

class FormFieldConfig(BaseModel):
    selector: str
    field_type: str  # text, select, radio, checkbox, file
    required: bool = False
    validation: Optional[str] = None

class FormConfig(BaseModel):
    name: str
    url: str
    fields: Dict[str, FormFieldConfig]
    submit_selector: str
    success_indicators: List[str]

class OCRConfig(BaseModel):
    provider: str = os.getenv("OCR_PROVIDER", "tesseract")  # tesseract, zhipu
    tesseract_cmd: Optional[str] = None  # Tesseract可执行文件路径
    
class AgentConfig(BaseModel):
    siliconflow: SiliconFlowConfig = SiliconFlowConfig()
    zhipu: ZhipuAIConfig = ZhipuAIConfig()
    chrome_mcp: ChromeMCPConfig = ChromeMCPConfig()
    ocr: OCRConfig = OCRConfig()
    max_retries: int = 3
    retry_delay: float = 2.0
    ocr_enabled: bool = True
    debug_mode: bool = False

# 表单配置示例
FORM_CONFIGS = {
    "expense_report": FormConfig(
        name="费用报销申请",
        url="https://example.com/expense-form",
        fields={
            "employee_name": FormFieldConfig(
                selector="input[name='employeeName']",
                field_type="text",
                required=True
            ),
            "employee_id": FormFieldConfig(
                selector="input[name='employeeId']",
                field_type="text",
                required=True
            ),
            "expense_type": FormFieldConfig(
                selector="select[name='expenseType']",
                field_type="select",
                required=True
            ),
            "amount": FormFieldConfig(
                selector="input[name='amount']",
                field_type="text",
                required=True,
                validation="^\\d+(\\.\\d{2})?$"
            ),
            "description": FormFieldConfig(
                selector="textarea[name='description']",
                field_type="text",
                required=True
            ),
            "receipt": FormFieldConfig(
                selector="input[type='file'][name='receipt']",
                field_type="file",
                required=False
            )
        },
        submit_selector="button[type='submit']",
        success_indicators=["申请已提交", "提交成功", "success"]
    )
}