import cv2
import pytesseract
import numpy as np
from PIL import Image
import re
from typing import Dict, Any, List, Optional
from loguru import logger
import base64
import io
from ..config import AgentConfig, OCRConfig
from ..clients import ZhipuOCRClient

class OCRService:
    def __init__(self, config: Optional[AgentConfig] = None):
        self.config = config or AgentConfig()
        
        # 配置Tesseract（根据系统调整路径）
        if self.config.ocr.tesseract_cmd:
            pytesseract.pytesseract.tesseract_cmd = self.config.ocr.tesseract_cmd
        
        # 初始化智谱AI客户端
        self.zhipu_client = None
        if self.config.ocr.provider == "zhipu":
            self.zhipu_client = ZhipuOCRClient(self.config.zhipu)
    
    async def _get_zhipu_client(self) -> ZhipuOCRClient:
        """获取智谱AI客户端"""
        if self.zhipu_client is None:
            self.zhipu_client = ZhipuOCRClient(self.config.zhipu)
        return self.zhipu_client
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """图像预处理"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 去噪
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # 二值化
        _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 形态学操作
        kernel = np.ones((2, 2), np.uint8)
        processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return processed
    
    async def extract_text_from_image(self, image_path: str) -> str:
        """从图像中提取文本"""
        try:
            if self.config.ocr.provider == "zhipu":
                # 使用智谱AI OCR
                client = await self._get_zhipu_client()
                async with client:
                    return await client.extract_text_from_image(image_path)
            else:
                # 使用Tesseract OCR
                return self._extract_text_with_tesseract(image_path)
        
        except Exception as e:
            logger.error(f"Error extracting text from image: {e}")
            return ""
    
    def _extract_text_with_tesseract(self, image_path: str) -> str:
        """使用Tesseract从图像中提取文本"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Cannot read image from {image_path}")
            
            # 预处理
            processed = self.preprocess_image(image)
            
            # OCR识别
            text = pytesseract.image_to_string(processed, lang='chi_sim+eng')
            
            logger.info(f"Tesseract extracted text from {image_path}: {len(text)} characters")
            return text
        
        except Exception as e:
            logger.error(f"Error in Tesseract OCR: {e}")
            return ""
    
    async def extract_text_from_base64(self, base64_data: str) -> str:
        """从base64图像数据中提取文本"""
        try:
            if self.config.ocr.provider == "zhipu":
                # 使用智谱AI OCR
                client = await self._get_zhipu_client()
                async with client:
                    return await client.extract_text_from_base64(base64_data)
            else:
                # 使用Tesseract OCR
                return self._extract_text_from_base64_with_tesseract(base64_data)
        
        except Exception as e:
            logger.error(f"Error extracting text from base64 image: {e}")
            return ""
    
    def _extract_text_from_base64_with_tesseract(self, base64_data: str) -> str:
        """使用Tesseract从base64图像数据中提取文本"""
        try:
            # 解码base64
            image_data = base64.b64decode(base64_data.split(',')[1] if ',' in base64_data else base64_data)
            
            # 转换为PIL图像
            pil_image = Image.open(io.BytesIO(image_data))
            
            # 转换为OpenCV格式
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            # 预处理
            processed = self.preprocess_image(cv_image)
            
            # OCR识别
            text = pytesseract.image_to_string(processed, lang='chi_sim+eng')
            
            logger.info(f"Tesseract extracted text from base64 image: {len(text)} characters")
            return text
        
        except Exception as e:
            logger.error(f"Error in Tesseract base64 OCR: {e}")
            return ""
    
    def parse_expense_receipt(self, text: str) -> Dict[str, Any]:
        """解析费用报销凭据"""
        result = {
            "amount": None,
            "date": None,
            "merchant": None,
            "category": None,
            "invoice_number": None
        }
        
        try:
            # 提取金额（支持多种格式）
            amount_patterns = [
                r'(?:金额|总额|合计|小计)[:：]?\s*¥?\s*(\d+(?:\.\d{2})?)',
                r'¥\s*(\d+(?:\.\d{2})?)',
                r'(\d+\.\d{2})\s*元',
                r'RMB\s*(\d+(?:\.\d{2})?)'
            ]
            
            for pattern in amount_patterns:
                match = re.search(pattern, text)
                if match:
                    result["amount"] = match.group(1)
                    break
            
            # 提取日期
            date_patterns = [
                r'(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
                r'(\d{4}-\d{2}-\d{2})',
                r'(\d{2}/\d{2}/\d{4})'
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, text)
                if match:
                    result["date"] = match.group(1)
                    break
            
            # 提取商户名称（通常在发票顶部）
            lines = text.split('\n')
            for i, line in enumerate(lines[:3]):  # 检查前三行
                if len(line.strip()) > 2 and not re.search(r'\d', line):
                    result["merchant"] = line.strip()
                    break
            
            # 提取发票号码
            invoice_patterns = [
                r'(?:发票号码|票据号|No\.?)[:：]?\s*([A-Z0-9]+)',
                r'([0-9]{8,})',  # 8位以上纯数字
            ]
            
            for pattern in invoice_patterns:
                match = re.search(pattern, text)
                if match:
                    result["invoice_number"] = match.group(1)
                    break
            
            # 简单分类
            if any(keyword in text for keyword in ['餐饮', '食品', '饭店', '酒店']):
                result["category"] = "餐饮费"
            elif any(keyword in text for keyword in ['交通', '出租', '地铁', '公交', '机票']):
                result["category"] = "交通费"
            elif any(keyword in text for keyword in ['住宿', '宾馆', '酒店']):
                result["category"] = "住宿费"
            elif any(keyword in text for keyword in ['办公', '文具', '用品']):
                result["category"] = "办公费"
            else:
                result["category"] = "其他"
            
            logger.info(f"Parsed receipt data: {result}")
            return result
        
        except Exception as e:
            logger.error(f"Error parsing receipt: {e}")
            return result
    
    def parse_id_card(self, text: str) -> Dict[str, Any]:
        """解析身份证信息"""
        result = {
            "name": None,
            "id_number": None,
            "gender": None,
            "birth_date": None,
            "address": None
        }
        
        try:
            # 提取姓名（通常在"姓名"后面）
            name_match = re.search(r'姓\s*名[:：]?\s*([^\s\d]+)', text)
            if name_match:
                result["name"] = name_match.group(1)
            
            # 提取身份证号码
            id_match = re.search(r'(\d{17}[\dXx])', text)
            if id_match:
                result["id_number"] = id_match.group(1)
            
            # 提取性别
            if '男' in text and '女' not in text:
                result["gender"] = "男"
            elif '女' in text and '男' not in text:
                result["gender"] = "女"
            
            # 提取出生日期
            birth_patterns = [
                r'(\d{4})年(\d{1,2})月(\d{1,2})日',
                r'(\d{4})\s*(\d{2})\s*(\d{2})'
            ]
            
            for pattern in birth_patterns:
                match = re.search(pattern, text)
                if match:
                    result["birth_date"] = f"{match.group(1)}-{match.group(2).zfill(2)}-{match.group(3).zfill(2)}"
                    break
            
            logger.info(f"Parsed ID card data: {result}")
            return result
        
        except Exception as e:
            logger.error(f"Error parsing ID card: {e}")
            return result
    
    async def extract_structured_data(self, image_path: str, data_type: str = "receipt") -> Dict[str, Any]:
        """提取结构化数据"""
        try:
            if self.config.ocr.provider == "zhipu":
                # 使用智谱AI直接提取结构化数据
                client = await self._get_zhipu_client()
                async with client:
                    return await client.extract_structured_data(image_path, data_type)
            else:
                # 使用Tesseract + 文本解析
                text = await self.extract_text_from_image(image_path)
                
                if data_type == "receipt":
                    return self.parse_expense_receipt(text)
                elif data_type == "id_card":
                    return self.parse_id_card(text)
                else:
                    return {"raw_text": text}
        
        except Exception as e:
            logger.error(f"Error extracting structured data: {e}")
            return {}