import asyncio
import json
from typing import Dict, Any, List, Optional, Callable
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential
from datetime import datetime

from ..config import AgentConfig, FORM_CONFIGS
from ..clients import ChromeMCPClient, SiliconFlowClient
from ..services import OCRService

class ReActAgent:
    def __init__(self, config: AgentConfig):
        self.config = config
        self.chrome_client = ChromeMCPClient(config.chrome_mcp)
        self.ocr_service = OCRService(config)
        self.session_state = {
            "current_form": None,
            "user_data": {},
            "form_progress": {},
            "conversation_history": [],
            "errors": []
        }
        
    async def start_session(self) -> bool:
        """启动会话"""
        try:
            # 连接Chrome MCP服务器
            if not await self.chrome_client.connect():
                logger.error("Failed to connect to Chrome MCP server")
                return False
            
            logger.info("ReAct Agent session started successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error starting session: {e}")
            return False
    
    async def end_session(self):
        """结束会话"""
        await self.chrome_client.disconnect()
        logger.info("ReAct Agent session ended")
    
    async def process_user_input(self, user_input: str, attachments: List[str] = None) -> str:
        """处理用户输入"""
        try:
            # 记录对话历史
            self.session_state["conversation_history"].append({
                "timestamp": datetime.now().isoformat(),
                "type": "user",
                "content": user_input,
                "attachments": attachments or []
            })
            
            # 处理附件（OCR识别）
            if attachments:
                await self._process_attachments(attachments)
            
            # 使用SiliconFlow分析意图
            async with SiliconFlowClient(self.config.siliconflow) as llm_client:
                intent_result = await llm_client.analyze_intent(user_input)
                
                # 更新会话状态
                if intent_result["intent"] != "unknown":
                    self.session_state["current_form"] = intent_result["intent"]
                    self.session_state["user_data"].update(intent_result["entities"])
                
                # 执行ReAct循环
                response = await self._react_loop(llm_client, user_input, intent_result)
                
                # 记录AI回复
                self.session_state["conversation_history"].append({
                    "timestamp": datetime.now().isoformat(),
                    "type": "assistant",
                    "content": response
                })
                
                return response
        
        except Exception as e:
            logger.error(f"Error processing user input: {e}")
            return f"抱歉，处理您的请求时出现了错误：{str(e)}"
    
    async def _process_attachments(self, attachments: List[str]):
        """处理附件（OCR识别）"""
        for attachment_path in attachments:
            try:
                # 根据文件类型判断OCR类型
                if any(keyword in attachment_path.lower() 
                      for keyword in ['receipt', '发票', '票据', '凭证']):
                    ocr_data = await self.ocr_service.extract_structured_data(
                        attachment_path, "receipt"
                    )
                    self.session_state["user_data"].update(ocr_data)
                    logger.info(f"Extracted receipt data: {ocr_data}")
                
                elif any(keyword in attachment_path.lower() 
                        for keyword in ['id', '身份证', '证件']):
                    ocr_data = await self.ocr_service.extract_structured_data(
                        attachment_path, "id_card"
                    )
                    self.session_state["user_data"].update(ocr_data)
                    logger.info(f"Extracted ID card data: {ocr_data}")
                
                else:
                    # 通用OCR
                    text = await self.ocr_service.extract_text_from_image(attachment_path)
                    self.session_state["user_data"]["attachment_text"] = text
                    logger.info(f"Extracted text from attachment: {len(text)} characters")
            
            except Exception as e:
                logger.error(f"Error processing attachment {attachment_path}: {e}")
    
    async def _react_loop(self, llm_client: SiliconFlowClient, 
                         user_input: str, intent_result: Dict[str, Any]) -> str:
        """ReAct循环：推理-行动-观察"""
        max_iterations = 5
        iteration = 0
        
        while iteration < max_iterations:
            iteration += 1
            logger.info(f"ReAct iteration {iteration}")
            
            # Reasoning: 分析当前状态并规划下一步
            current_state = {
                "user_input": user_input,
                "intent_result": intent_result,
                "session_state": self.session_state,
                "current_url": await self._get_current_url_safe()
            }
            
            action_plan = await llm_client.plan_next_action(current_state)
            logger.info(f"Action plan: {action_plan}")
            
            # Action: 执行动作
            action_result = await self._execute_action(action_plan)
            
            # Observation: 观察结果
            if action_result.get("success"):
                if action_plan["action"] == "submit":
                    # 表单提交成功，结束循环
                    return "表单已成功提交！"
                elif action_plan["action"] == "ask_user":
                    # 需要用户输入，结束循环
                    return action_result.get("response", "请提供更多信息。")
                elif action_plan["action"] == "complete":
                    # 任务完成
                    return action_result.get("response", "任务已完成。")
            else:
                # 动作失败，记录错误
                error_msg = action_result.get("error", "Unknown error")
                self.session_state["errors"].append({
                    "timestamp": datetime.now().isoformat(),
                    "action": action_plan["action"],
                    "error": error_msg
                })
                
                # 如果是关键错误，询问用户
                if self._is_critical_error(error_msg):
                    return f"遇到问题：{error_msg}。请告诉我如何处理，或提供更多信息。"
        
        return "处理过程比较复杂，让我们一步步来完成。请告诉我下一步应该怎么做？"
    
    async def _execute_action(self, action_plan: Dict[str, Any]) -> Dict[str, Any]:
        """执行动作"""
        action = action_plan.get("action")
        target = action_plan.get("target", "")
        value = action_plan.get("value", "")
        
        try:
            if action == "navigate":
                return await self._action_navigate(target)
            elif action == "fill_field":
                return await self._action_fill_field(target, value)
            elif action == "click":
                return await self._action_click(target)
            elif action == "submit":
                return await self._action_submit(target)
            elif action == "ask_user":
                return await self._action_ask_user(action_plan.get("reason", ""))
            elif action == "wait":
                return await self._action_wait(int(value) if value else 3)
            elif action == "analyze_page":
                return await self._action_analyze_page()
            else:
                return {"success": False, "error": f"Unknown action: {action}"}
        
        except Exception as e:
            logger.error(f"Error executing action {action}: {e}")
            return {"success": False, "error": str(e)}
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _action_navigate(self, url: str) -> Dict[str, Any]:
        """导航到指定URL"""
        try:
            await self.chrome_client.navigate_to(url)
            await self.chrome_client.wait_for_load()
            
            current_url = await self.chrome_client.get_current_url()
            logger.info(f"Navigated to: {current_url}")
            
            return {"success": True, "current_url": current_url}
        
        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _action_fill_field(self, selector: str, value: str) -> Dict[str, Any]:
        """填写表单字段"""
        try:
            # 等待元素出现
            await self.chrome_client.wait_for_element(selector, timeout=10)
            
            # 检查元素是否可见
            if not await self.chrome_client.is_element_visible(selector):
                return {"success": False, "error": f"Element {selector} is not visible"}
            
            # 根据字段类型执行不同操作
            element_info = await self.chrome_client.find_element(selector)
            
            if "select" in selector.lower():
                await self.chrome_client.select_option(selector, value)
            elif "file" in selector.lower():
                await self.chrome_client.upload_file(selector, value)
            else:
                await self.chrome_client.input_text(selector, value)
            
            logger.info(f"Filled field {selector} with value: {value}")
            return {"success": True, "field": selector, "value": value}
        
        except Exception as e:
            logger.error(f"Failed to fill field {selector}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _action_click(self, selector: str) -> Dict[str, Any]:
        """点击元素"""
        try:
            await self.chrome_client.wait_for_element(selector, timeout=10)
            await self.chrome_client.click_element(selector)
            
            # 等待页面变化
            await asyncio.sleep(1)
            
            logger.info(f"Clicked element: {selector}")
            return {"success": True, "clicked": selector}
        
        except Exception as e:
            logger.error(f"Failed to click element {selector}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _action_submit(self, selector: str) -> Dict[str, Any]:
        """提交表单"""
        try:
            # 获取当前表单配置
            form_config = FORM_CONFIGS.get(self.session_state["current_form"])
            if not form_config:
                return {"success": False, "error": "No form configuration found"}
            
            # 点击提交按钮
            submit_selector = selector or form_config.submit_selector
            await self.chrome_client.click_element(submit_selector)
            
            # 等待提交结果
            await asyncio.sleep(3)
            
            # 检查成功指示器
            current_url = await self.chrome_client.get_current_url()
            page_text = await self.chrome_client.get_element_text("body")
            
            for indicator in form_config.success_indicators:
                if indicator in page_text or indicator in current_url:
                    logger.info("Form submitted successfully")
                    return {"success": True, "message": "表单提交成功"}
            
            # 检查是否有错误消息
            error_selectors = [".error", ".alert-danger", "[class*='error']"]
            for error_selector in error_selectors:
                try:
                    error_text = await self.chrome_client.get_element_text(error_selector)
                    if error_text:
                        return {"success": False, "error": f"Form submission error: {error_text}"}
                except:
                    continue
            
            return {"success": True, "message": "表单已提交，请确认结果"}
        
        except Exception as e:
            logger.error(f"Failed to submit form: {e}")
            return {"success": False, "error": str(e)}
    
    async def _action_ask_user(self, reason: str) -> Dict[str, Any]:
        """询问用户"""
        async with SiliconFlowClient(self.config.siliconflow) as llm_client:
            context = {
                "reason": reason,
                "session_state": self.session_state,
                "missing_info": []
            }
            
            # 检查缺失的必要信息
            if self.session_state["current_form"]:
                form_config = FORM_CONFIGS.get(self.session_state["current_form"])
                if form_config:
                    for field_name, field_config in form_config.fields.items():
                        if field_config.required and field_name not in self.session_state["user_data"]:
                            context["missing_info"].append(field_name)
            
            response = await llm_client.generate_response("需要更多信息", context)
            
            return {"success": True, "response": response}
    
    async def _action_wait(self, seconds: int) -> Dict[str, Any]:
        """等待"""
        await asyncio.sleep(seconds)
        return {"success": True, "waited": seconds}
    
    async def _action_analyze_page(self) -> Dict[str, Any]:
        """分析当前页面"""
        try:
            page_source = await self.chrome_client.get_page_source()
            current_url = await self.chrome_client.get_current_url()
            
            # 使用LLM分析页面内容
            async with SiliconFlowClient(self.config.siliconflow) as llm_client:
                form_config = FORM_CONFIGS.get(self.session_state["current_form"], {})
                form_data = await llm_client.extract_form_data(page_source, form_config)
                
                self.session_state["form_progress"] = form_data
                
            return {"success": True, "form_data": form_data, "url": current_url}
        
        except Exception as e:
            logger.error(f"Failed to analyze page: {e}")
            return {"success": False, "error": str(e)}
    
    async def _get_current_url_safe(self) -> str:
        """安全地获取当前URL"""
        try:
            return await self.chrome_client.get_current_url()
        except:
            return ""
    
    def _is_critical_error(self, error_msg: str) -> bool:
        """判断是否为关键错误"""
        critical_keywords = [
            "not found", "timeout", "permission denied", 
            "authentication required", "access denied"
        ]
        return any(keyword in error_msg.lower() for keyword in critical_keywords)
    
    def get_session_summary(self) -> Dict[str, Any]:
        """获取会话摘要"""
        return {
            "current_form": self.session_state["current_form"],
            "user_data_count": len(self.session_state["user_data"]),
            "conversation_count": len(self.session_state["conversation_history"]),
            "errors": self.session_state["errors"],
            "form_progress": self.session_state["form_progress"]
        }