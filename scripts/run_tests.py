#!/usr/bin/env python3
"""
测试运行脚本
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, cwd=project_root)
        print(f"✅ {description} - 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - 失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Form Filling Agent - 测试套件")
    
    tests = [
        ("python tests/test.py api", "硅基流动API测试"),
        ("python tests/test.py chrome", "Chrome MCP测试"),
        ("python tests/test.py ocr", "OCR功能测试"),
        ("python tests/test.py basic", "基本功能测试"),
        ("python tests/test_zhipu_ocr.py", "智谱AI OCR测试"),
        ("python examples/ocr_example.py", "OCR示例演示"),
    ]
    
    results = []
    
    for cmd, description in tests:
        success = run_command(cmd, description)
        results.append((description, success))
    
    # 显示测试结果摘要
    print(f"\n{'='*60}")
    print("📊 测试结果摘要")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for description, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {description}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)