# AI表单自动填报Agent

基于ReAct架构的智能表单自动填报系统，使用Chrome MCP Server控制浏览器，硅基流动API提供大模型能力。

## 功能特性

- 🤖 **智能意图识别**: 自动理解用户需求并提取关键信息
- 🔍 **OCR文档识别**: 自动识别票据、发票等凭证信息
- 🌐 **浏览器自动化**: 通过Chrome MCP Server控制浏览器操作
- 📝 **智能表单填写**: 自动映射数据到表单字段并填写
- 💬 **交互式对话**: 遇到问题时主动询问用户
- 🔄 **错误恢复**: 支持重试机制和异常处理
- 🎯 **ReAct架构**: 推理-行动-观察的循环决策机制

## 系统架构

```
用户输入 → 意图识别 → 信息提取(OCR) → 浏览器操作 → 表单填写 → 提交确认
    ↑                                                           ↓
    └─────────────── 交互式询问 ←─────────── 遇到问题时 ←──────────┘
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd form-filling-agent

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装Python依赖
pip install -r requirements.txt

# 安装Chrome MCP Server
npm install -g @hangwin/mcp-chrome
```

### 2. 配置设置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，填入您的API密钥
vim .env
```

配置说明：
- `SILICONFLOW_API_KEY`: 硅基流动API密钥
- `ZHIPU_API_KEY`: 智谱AI API密钥（用于GLM-4.1V-Thinking OCR）
- `CHROME_MCP_HOST/PORT`: Chrome MCP服务器地址
- `OCR_PROVIDER`: OCR服务提供商（tesseract 或 zhipu）
- 其他可选配置项

### 3. 启动Chrome MCP Server

```bash
# 手动启动Chrome MCP Server
```

### 4. 运行测试

```bash
# 运行所有测试
python scripts/run_tests.py

# 运行特定测试
python tests/test.py api      # 测试API连接
python tests/test.py chrome   # 测试Chrome MCP
python tests/test.py ocr      # 测试OCR功能
python tests/test.py basic    # 测试基本功能

# 智谱AI OCR专项测试
python tests/test_zhipu_ocr.py

# OCR服务对比示例
python examples/ocr_example.py
```

### 5. 启动应用

#### 命令行模式
```bash
# 交互式聊天
python main.py

# 单命令模式
python main.py "创建一个报销申请"
```

#### Web API模式
```bash
# 启动API服务器
python api_server.py

# 访问API文档
open http://localhost:8000/docs
```

## 使用示例

### 基本使用

```bash
python main.py
```

```
您: 我要创建一个报销申请
助手: 好的，我来帮您创建报销申请。请提供以下信息：
- 您的姓名
- 工号
- 报销金额
- 报销类型
- 相关凭据

您: 我叫张三，工号001，报销差旅费500元
助手: 收到您的信息。我注意到您还需要提供报销凭据。请上传相关票据，或者告诉我票据的详细信息。

您: 附件:/path/to/receipt.jpg 这是我的火车票
助手: 正在识别您的火车票信息...
识别到：金额500元，日期2024-01-15，北京-上海
现在开始填写报销表单...
```

### API调用示例

```python
import requests

# 基本聊天
response = requests.post("http://localhost:8000/chat", 
    json={"message": "创建报销申请"})
print(response.json())

# 带文件上传
files = {"files": open("receipt.jpg", "rb")}
data = {"message": "这是我的报销凭据"}
response = requests.post("http://localhost:8000/chat_with_files", 
    data=data, files=files)
print(response.json())
```

## 项目结构

```
form-filling-agent/
├── src/                    # 源代码
│   ├── agents/            # 智能代理
│   ├── clients/           # 外部服务客户端
│   ├── services/          # 业务服务
│   └── config/            # 配置管理
├── tests/                 # 测试文件
├── examples/              # 使用示例
├── docs/                  # 项目文档
└── scripts/               # 工具脚本
```

详细结构说明请参考：[项目结构文档](docs/PROJECT_STRUCTURE.md)

## 配置表单

在 `src/config/settings.py` 中添加新的表单配置：

```python
FORM_CONFIGS["my_form"] = FormConfig(
    name="我的表单",
    url="https://example.com/my-form",
    fields={
        "name": FormFieldConfig(
            selector="input[name='name']",
            field_type="text",
            required=True
        ),
        "amount": FormFieldConfig(
            selector="input[name='amount']",
            field_type="text",
            required=True,
            validation="^\\d+(\\.\\d{2})?$"
        )
    },
    submit_selector="button[type='submit']",
    success_indicators=["提交成功", "success"]
)
```

## OCR配置

系统支持两种OCR服务：

### 1. Tesseract OCR（本地）

#### 安装Tesseract

##### Ubuntu/Debian
```bash
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim
```

##### CentOS/RHEL
```bash
sudo yum install tesseract tesseract-langpack-chi_sim
```

##### Windows
1. 下载安装包：https://github.com/UB-Mannheim/tesseract/wiki
2. 安装后配置环境变量或在代码中指定路径

##### macOS
```bash
brew install tesseract tesseract-lang
```

### 2. 智谱AI GLM-4.1V-Thinking（云端）

智谱AI的GLM-4.1V-Thinking模型提供更准确的OCR识别，特别适合：
- 复杂布局的文档
- 手写文字识别
- 结构化数据提取

#### 配置智谱AI OCR

1. 获取API密钥：访问 [智谱AI开放平台](https://www.bigmodel.cn/)
2. 在`.env`文件中配置：
   ```bash
   ZHIPU_API_KEY=your_api_key_here
   OCR_PROVIDER=zhipu
   ```

#### OCR提供商选择

在`.env`文件中设置`OCR_PROVIDER`：
- `tesseract`: 使用本地Tesseract OCR（免费，需要安装）
- `zhipu`: 使用智谱AI GLM-4.1V-Thinking（付费，更准确）

## 故障排除

### 常见问题

1. **Chrome MCP连接失败**
   ```bash
   # 检查服务是否运行
   curl http://localhost:3000
   
   # 重启服务
   pkill -f mcp-chrome
   npx @hangwin/mcp-chrome --port 3000
   ```

2. **硅基流动API调用失败**
   - 检查API密钥是否正确
   - 确认账户余额充足
   - 检查网络连接

3. **OCR识别效果差**
   - 确保图像清晰
   - 检查Tesseract安装和语言包
   - 调整图像预处理参数

4. **表单填写失败**
   - 检查CSS选择器是否正确
   - 确认页面已完全加载
   - 检查表单字段类型配置

### 调试模式

启用调试模式获取详细日志：

```bash
# 设置环境变量
export DEBUG_MODE=true

# 或在.env文件中设置
echo "DEBUG_MODE=true" >> .env
```

### 日志文件

日志文件位置：`logs/agent_YYYY-MM-DD.log`

```bash
# 查看实时日志
tail -f logs/agent_$(date +%Y-%m-%d).log
```

## API文档

启动API服务器后，访问 http://localhost:8000/docs 查看完整的API文档。

### 主要接口

- `POST /chat` - 基本聊天接口
- `POST /chat_with_files` - 带文件上传的聊天
- `GET /session/status` - 获取会话状态
- `POST /session/clear` - 清除会话状态
- `GET /forms` - 获取可用表单列表

## 扩展开发

### 添加新的表单类型

1. 在 `config.py` 中添加表单配置
2. 更新意图识别的提示词
3. 添加特定的数据处理逻辑

### 集成新的OCR服务

1. 继承 `OCRService` 类
2. 实现特定的识别方法
3. 在配置中指定使用的OCR服务

### 添加新的浏览器操作

1. 在 `ChromeMCPClient` 中添加新方法
2. 在 `ReActAgent` 中添加对应的动作处理
3. 更新动作规划的逻辑

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题，请通过Issue或邮件联系。