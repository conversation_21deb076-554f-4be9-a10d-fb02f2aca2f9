#!/usr/bin/env python3
"""
OCR服务使用示例
演示如何在Tesseract和智谱AI之间切换
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config import AgentConfig
from src.services import OCRService
import json

async def demo_ocr_services():
    """演示不同OCR服务的使用"""
    print("=== OCR服务对比演示 ===\n")
    
    # 检查测试图像
    test_image_path = "test_receipt.jpg"
    
    if not Path(test_image_path).exists():
        print(f"⚠️  测试图像文件不存在: {test_image_path}")
        print("请在项目根目录放置一个名为 test_receipt.jpg 的测试图像")
        return
    
    print(f"📸 使用测试图像: {test_image_path}\n")
    
    # 测试Tesseract OCR
    print("--- 1. Tesseract OCR (本地) ---")
    try:
        # 临时设置为Tesseract
        os.environ["OCR_PROVIDER"] = "tesseract"
        config1 = AgentConfig()
        ocr1 = OCRService(config1)
        
        print("🔧 提供商: Tesseract")
        text1 = await ocr1.extract_text_from_image(test_image_path)
        print(f"📄 提取文本 ({len(text1)} 字符):")
        print(text1[:200] + "..." if len(text1) > 200 else text1)
        
        receipt_data1 = await ocr1.extract_structured_data(test_image_path, "receipt")
        print("💰 结构化数据:")
        print(json.dumps(receipt_data1, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"❌ Tesseract OCR失败: {e}")
    
    print("\n" + "="*60 + "\n")
    
    # 测试智谱AI OCR
    print("--- 2. 智谱AI GLM-4.1V-Thinking (云端) ---")
    try:
        # 临时设置为智谱AI
        os.environ["OCR_PROVIDER"] = "zhipu"
        config2 = AgentConfig()
        
        if not config2.zhipu.api_key:
            print("⚠️  未配置智谱AI API密钥，跳过测试")
            print("请在.env文件中设置 ZHIPU_API_KEY")
        else:
            ocr2 = OCRService(config2)
            
            print("🔧 提供商: 智谱AI GLM-4.1V-Thinking")
            text2 = await ocr2.extract_text_from_image(test_image_path)
            print(f"📄 提取文本 ({len(text2)} 字符):")
            print(text2[:200] + "..." if len(text2) > 200 else text2)
            
            receipt_data2 = await ocr2.extract_structured_data(test_image_path, "receipt")
            print("💰 结构化数据:")
            print(json.dumps(receipt_data2, indent=2, ensure_ascii=False))
    
    except Exception as e:
        print(f"❌ 智谱AI OCR失败: {e}")
    
    print("\n" + "="*60 + "\n")
    
    print("📝 使用说明:")
    print("1. 在.env文件中设置 OCR_PROVIDER=tesseract 使用本地Tesseract")
    print("2. 在.env文件中设置 OCR_PROVIDER=zhipu 使用智谱AI")
    print("3. 智谱AI需要配置 ZHIPU_API_KEY")

async def main():
    """主函数"""
    await demo_ocr_services()

if __name__ == "__main__":
    # Windows兼容性
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())