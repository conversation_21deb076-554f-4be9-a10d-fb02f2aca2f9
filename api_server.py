from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import asyncio
import tempfile
import os
from pathlib import Path

from main import FormFillingApp

app = FastAPI(
    title="Form Filling Agent API",
    description="AI Agent for automatic form filling",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局Agent实例
agent_app: Optional[FormFillingApp] = None

class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_summary: dict
    success: bool
    error: Optional[str] = None

@app.on_event("startup")
async def startup_event():
    """启动时初始化Agent"""
    global agent_app
    agent_app = FormFillingApp()
    
    if not await agent_app.start():
        raise Exception("Failed to start FormFillingApp")

@app.on_event("shutdown")
async def shutdown_event():
    """关闭时清理资源"""
    global agent_app
    if agent_app:
        await agent_app.stop()

@app.get("/")
async def root():
    """根路径"""
    return {"message": "Form Filling Agent API is running"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "agent_ready": agent_app is not None}

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口"""
    if not agent_app:
        raise HTTPException(status_code=500, detail="Agent not initialized")
    
    try:
        result = await agent_app.run_single_command(request.message)
        
        return ChatResponse(
            response=result["response"],
            session_summary=result["session_summary"],
            success=result["success"],
            error=result.get("error")
        )
    
    except Exception as e:
        return ChatResponse(
            response="",
            session_summary={},
            success=False,
            error=str(e)
        )

@app.post("/chat_with_files", response_model=ChatResponse)
async def chat_with_files(
    message: str,
    files: List[UploadFile] = File(...),
    session_id: Optional[str] = None
):
    """带文件上传的聊天接口"""
    if not agent_app:
        raise HTTPException(status_code=500, detail="Agent not initialized")
    
    # 保存上传的文件到临时目录
    temp_files = []
    try:
        for file in files:
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp:
                content = await file.read()
                tmp.write(content)
                temp_files.append(tmp.name)
        
        # 处理消息
        result = await agent_app.run_single_command(message, temp_files)
        
        return ChatResponse(
            response=result["response"],
            session_summary=result["session_summary"],
            success=result["success"],
            error=result.get("error")
        )
    
    except Exception as e:
        return ChatResponse(
            response="",
            session_summary={},
            success=False,
            error=str(e)
        )
    
    finally:
        # 清理临时文件
        for temp_file in temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass

@app.get("/session/status")
async def get_session_status():
    """获取会话状态"""
    if not agent_app:
        raise HTTPException(status_code=500, detail="Agent not initialized")
    
    return agent_app.agent.get_session_summary()

@app.post("/session/clear")
async def clear_session():
    """清除会话状态"""
    if not agent_app:
        raise HTTPException(status_code=500, detail="Agent not initialized")
    
    agent_app.agent.session_state = {
        "current_form": None,
        "user_data": {},
        "form_progress": {},
        "conversation_history": [],
        "errors": []
    }
    
    return {"success": True, "message": "Session cleared"}

@app.get("/forms")
async def get_available_forms():
    """获取可用的表单类型"""
    from src.config import FORM_CONFIGS
    
    forms = []
    for form_id, form_config in FORM_CONFIGS.items():
        forms.append({
            "id": form_id,
            "name": form_config.name,
            "url": form_config.url,
            "fields": len(form_config.fields)
        })
    
    return {"forms": forms}

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )