# 硅基流动API配置
SILICONFLOW_API_KEY=your_siliconflow_api_key_here
SILICONFLOW_MODEL_NAME=Qwen/Qwen3-8B

# 智谱AI配置
ZHIPU_API_KEY=your_zhipu_api_key_here
ZHIPU_MODEL_NAME=GLM-4.1V-Thinking-Flash

# Chrome MCP配置
CHROME_MCP_HOST=127.0.0.1
CHROME_MCP_PORT=12306
CHROME_MCP_PROTOCOL=http
CHROME_MCP_TIMEOUT=30
CHROME_MCP_SSE_READ_TIMEOUT=300
CHROME_MCP_MAX_RETRIES=3
CHROME_MCP_RETRY_DELAY=2.0
CHROME_MCP_HEALTH_CHECK_INTERVAL=60
CHROME_MCP_DEBUG=false

# OCR配置
# 可选值: tesseract, zhipu
OCR_PROVIDER=tesseract

# Tesseract路径配置（可选，如果Tesseract不在PATH中）
# TESSERACT_CMD=/usr/local/bin/tesseract

# 调试模式
DEBUG_MODE=false