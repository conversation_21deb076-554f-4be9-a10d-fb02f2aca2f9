import asyncio
import sys
import json
from pathlib import Path
from typing import List
from loguru import logger
from src.config import AgentConfig
from src.agents import ReActAgent

class FormFillingApp:
    def __init__(self):
        self.config = AgentConfig()
        self.agent = ReActAgent(self.config)
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logger.remove()
        logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO" if not self.config.debug_mode else "DEBUG"
        )
        
        # 添加文件日志
        logger.add(
            "logs/agent_{time:YYYY-MM-DD}.log",
            rotation="1 day",
            retention="7 days",
            level="DEBUG"
        )
    
    async def start(self):
        """启动应用"""
        logger.info("启动表单自动填报Agent...")
        
        # 创建日志目录
        Path("logs").mkdir(exist_ok=True)
        
        # 启动Agent会话
        if not await self.agent.start_session():
            logger.error("Failed to start agent session")
            return False
        
        logger.info("Agent启动成功！")
        return True
    
    async def stop(self):
        """停止应用"""
        await self.agent.end_session()
        logger.info("Agent已停止")
    
    async def chat_loop(self):
        """聊天循环"""
        print("\n=== 表单自动填报助手 ===")
        print("说明：您可以说'创建一个报销申请'、'填写请假单'等来开始")
        print("输入 'quit' 或 'exit' 退出")
        print("输入 'help' 查看帮助")
        print("输入 'status' 查看当前状态")
        print("=" * 40)
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n您: ").strip()
                
                if not user_input:
                    continue
                
                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("再见！")
                    break
                
                elif user_input.lower() in ['help', '帮助']:
                    self.show_help()
                    continue
                
                elif user_input.lower() in ['status', '状态']:
                    self.show_status()
                    continue
                
                elif user_input.lower() in ['clear', '清除']:
                    self.agent.session_state = {
                        "current_form": None,
                        "user_data": {},
                        "form_progress": {},
                        "conversation_history": [],
                        "errors": []
                    }
                    print("会话状态已清除")
                    continue
                
                elif user_input.lower() in ['list_tools', 'tools', '工具列表', '工具']:
                    await self.show_tools()
                    continue
                
                # 检查是否有附件
                attachments = []
                if "附件:" in user_input or "文件:" in user_input:
                    parts = user_input.split(":")
                    if len(parts) > 1:
                        user_input = parts[0].strip()
                        attachments = [path.strip() for path in parts[1].split(",")]
                
                # 处理用户输入
                print("助手: 正在处理中...")
                response = await self.agent.process_user_input(user_input, attachments)
                print(f"助手: {response}")
            
            except KeyboardInterrupt:
                print("\n\n收到中断信号，正在退出...")
                break
            
            except Exception as e:
                logger.error(f"Error in chat loop: {e}")
                print(f"助手: 抱歉，出现了错误：{str(e)}")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
可用命令：
- quit/exit: 退出程序
- help: 显示此帮助信息
- status: 查看当前状态
- clear: 清除会话状态
- list_tools/tools: 显示可用的MCP工具列表

使用示例：
- "创建一个报销申请"
- "我要填写请假单，姓名是张三，工号是001"
- "附件:/path/to/receipt.jpg 这是我的报销凭据"

支持的表单类型：
- 费用报销申请 (expense_report)
- 请假申请 (leave_request)
- 其他（可通过配置文件添加）
        """
        print(help_text)
    
    def show_status(self):
        """显示当前状态"""
        summary = self.agent.get_session_summary()
        
        print("\n=== 当前状态 ===")
        print(f"当前表单: {summary['current_form'] or '无'}")
        print(f"用户数据: {summary['user_data_count']} 项")
        print(f"对话轮数: {summary['conversation_count']}")
        print(f"错误次数: {len(summary['errors'])}")
        
        if summary['errors']:
            print("\n最近的错误:")
            for error in summary['errors'][-3:]:
                print(f"  - {error['timestamp']}: {error['error']}")
        
        print("=" * 20)
    
    async def run_single_command(self, command: str, attachments: List[str] = None):
        """运行单个命令（用于API调用）"""
        try:
            response = await self.agent.process_user_input(command, attachments)
            return {
                "success": True,
                "response": response,
                "session_summary": self.agent.get_session_summary()
            }
        except Exception as e:
            logger.error(f"Error running command: {e}")
            return {
                "success": False,
                "error": str(e),
                "session_summary": self.agent.get_session_summary()
            }

    async def show_tools(self):
        """显示可用的MCP工具"""
        try:
            print("\n=== 可用的Chrome MCP工具 ===")
            print("正在获取工具列表...")
            
            # 获取工具列表
            tools = await self.agent.chrome_client.list_available_tools()
            
            if not tools:
                print("❌ 未找到可用工具")
                return
            
            print(f"✅ 找到 {len(tools)} 个可用工具：\n")
            
            # 格式化显示工具信息
            for i, tool in enumerate(tools, 1):
                name = tool.get('name', 'Unknown')
                description = tool.get('description', '无描述')
                
                print(f"{i:2d}. {name}")
                print(f"    描述: {description}")
                
                # 显示参数信息
                input_schema = tool.get('inputSchema', {})
                properties = input_schema.get('properties', {})
                required = input_schema.get('required', [])
                
                if properties:
                    print("    参数:")
                    for param_name, param_info in properties.items():
                        param_type = param_info.get('type', 'unknown')
                        param_desc = param_info.get('description', '无描述')
                        is_required = param_name in required
                        required_mark = " (必需)" if is_required else " (可选)"
                        print(f"      - {param_name} ({param_type}){required_mark}: {param_desc}")
                else:
                    print("    参数: 无")
                
                print()  # 空行分隔
            
            print("=" * 40)
            
        except Exception as e:
            logger.error(f"获取工具列表失败: {e}")
            print(f"❌ 获取工具列表失败: {str(e)}")
            print("💡 请确保Chrome MCP服务器正在运行")

async def main():
    """主函数"""
    app = FormFillingApp()
    
    try:
        # 启动应用
        if not await app.start():
            return 1
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            # 单命令模式
            command = " ".join(sys.argv[1:])
            result = await app.run_single_command(command)
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            # 交互模式
            await app.chat_loop()
    
    except Exception as e:
        logger.error(f"Application error: {e}")
        return 1
    
    finally:
        await app.stop()
    
    return 0

if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)