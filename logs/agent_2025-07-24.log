2025-07-24 22:39:44.877 | INFO     | __main__:start:35 - 启动表单自动填报Agent...
2025-07-24 22:39:44.878 | DEBUG    | src.clients.connection_manager:_check_all_connections:69 - Health check failed for transport
2025-07-24 22:39:44.878 | INFO     | src.clients.connection_manager:ensure_connected:58 - Connection not available, attempting to connect...
2025-07-24 22:39:44.980 | INFO     | src.clients.mcp_transport:connect:51 - Successfully connected to MCP server at http://127.0.0.1:12306/mcp
2025-07-24 22:39:44.980 | DEBUG    | src.clients.connection_manager:_connect_all:86 - Successfully connected transport
2025-07-24 22:39:44.980 | INFO     | src.clients.mcp_client:connect:36 - Connected to Chrome MCP Server at http://127.0.0.1:12306/mcp
2025-07-24 22:39:44.980 | INFO     | src.agents.react_agent:start_session:33 - ReAct Agent session started successfully
2025-07-24 22:39:44.980 | INFO     | __main__:start:45 - Agent启动成功！
2025-07-24 22:41:39.455 | INFO     | src.clients.connection_manager:shutdown:214 - Shutting down connection manager...
2025-07-24 22:41:39.455 | DEBUG    | src.clients.connection_manager:_health_check_loop:170 - Health check loop cancelled
2025-07-24 22:41:39.456 | INFO     | src.clients.mcp_transport:disconnect:215 - Disconnected from MCP server
2025-07-24 22:41:39.456 | DEBUG    | src.clients.connection_manager:_disconnect_all:147 - Disconnected transport
2025-07-24 22:41:39.456 | INFO     | src.clients.connection_manager:shutdown:229 - Connection manager shutdown complete
2025-07-24 22:41:39.456 | INFO     | src.clients.mcp_client:disconnect:46 - Disconnected from Chrome MCP Server
2025-07-24 22:41:39.457 | INFO     | src.agents.react_agent:end_session:43 - ReAct Agent session ended
2025-07-24 22:41:39.457 | INFO     | __main__:stop:51 - Agent已停止
2025-07-24 22:41:52.885 | INFO     | __main__:start:35 - 启动表单自动填报Agent...
2025-07-24 22:41:52.892 | DEBUG    | src.clients.connection_manager:_check_all_connections:69 - Health check failed for transport
2025-07-24 22:41:52.904 | INFO     | src.clients.connection_manager:ensure_connected:58 - Connection not available, attempting to connect...
2025-07-24 22:41:53.008 | INFO     | src.clients.mcp_transport:connect:51 - Successfully connected to MCP server at http://127.0.0.1:12306/mcp
2025-07-24 22:41:53.022 | DEBUG    | src.clients.connection_manager:_connect_all:86 - Successfully connected transport
2025-07-24 22:41:53.041 | INFO     | src.clients.mcp_client:connect:36 - Connected to Chrome MCP Server at http://127.0.0.1:12306/mcp
2025-07-24 22:41:53.061 | INFO     | src.agents.react_agent:start_session:33 - ReAct Agent session started successfully
2025-07-24 22:41:53.062 | INFO     | __main__:start:45 - Agent启动成功！
2025-07-24 22:42:05.067 | INFO     | src.clients.connection_manager:shutdown:214 - Shutting down connection manager...
2025-07-24 22:42:05.068 | INFO     | src.clients.mcp_transport:disconnect:215 - Disconnected from MCP server
2025-07-24 22:42:05.068 | DEBUG    | src.clients.connection_manager:_disconnect_all:147 - Disconnected transport
2025-07-24 22:42:05.068 | INFO     | src.clients.connection_manager:shutdown:229 - Connection manager shutdown complete
2025-07-24 22:42:05.068 | INFO     | src.clients.mcp_client:disconnect:46 - Disconnected from Chrome MCP Server
2025-07-24 22:42:05.068 | INFO     | src.agents.react_agent:end_session:43 - ReAct Agent session ended
2025-07-24 22:42:05.069 | INFO     | __main__:stop:51 - Agent已停止
2025-07-24 22:44:39.453 | INFO     | __main__:start:35 - 启动表单自动填报Agent...
2025-07-24 22:44:39.454 | DEBUG    | src.clients.connection_manager:_check_all_connections:69 - Health check failed for transport
2025-07-24 22:44:39.454 | INFO     | src.clients.connection_manager:ensure_connected:58 - Connection not available, attempting to connect...
2025-07-24 22:44:39.536 | INFO     | src.clients.mcp_transport:connect:51 - Successfully connected to MCP server at http://127.0.0.1:12306/mcp
2025-07-24 22:44:39.536 | DEBUG    | src.clients.connection_manager:_connect_all:86 - Successfully connected transport
2025-07-24 22:44:39.536 | INFO     | src.clients.mcp_client:connect:36 - Connected to Chrome MCP Server at http://127.0.0.1:12306/mcp
2025-07-24 22:44:39.537 | INFO     | src.agents.react_agent:start_session:33 - ReAct Agent session started successfully
2025-07-24 22:44:39.537 | INFO     | __main__:start:45 - Agent启动成功！
2025-07-24 22:51:19.269 | ERROR    | __main__:chat_loop:116 - Error in chat loop: 'utf-8' codec can't decode bytes in position 49-50: invalid continuation byte
2025-07-24 22:52:16.934 | INFO     | src.agents.react_agent:_react_loop:123 - ReAct iteration 1
2025-07-24 22:52:16.946 | INFO     | src.clients.request_manager:log_request_success:115 - MCP request chrome_get_current_url completed in 11.1ms
2025-07-24 22:53:04.161 | INFO     | src.agents.react_agent:_react_loop:134 - Action plan: {'action': 'navigate', 'target': 'http://localhost:3000/', 'value': '', 'reason': '用户需要先访问指定的网页地址以开始报销申请流程', 'next_steps': ['点击新建报销申请链接', '填写员工姓名字段', '填写金额字段', '填写描述字段', '上传收据文件']}
2025-07-24 22:53:04.161 | INFO     | src.clients.mcp_client:navigate_to:75 - Navigating to: http://localhost:3000/
2025-07-24 22:53:04.240 | INFO     | src.clients.request_manager:log_request_success:115 - MCP request chrome_navigate completed in 79.0ms
2025-07-24 22:53:04.265 | INFO     | src.clients.request_manager:log_request_success:115 - MCP request chrome_wait_for_load completed in 20.8ms
2025-07-24 22:53:04.266 | DEBUG    | src.clients.request_manager:send_request:408 - Cache hit for chrome_get_current_url
2025-07-24 22:53:04.266 | INFO     | src.agents.react_agent:_action_navigate:201 - Navigated to: 
2025-07-24 22:53:04.267 | INFO     | src.agents.react_agent:_react_loop:123 - ReAct iteration 2
2025-07-24 22:53:04.267 | DEBUG    | src.clients.request_manager:send_request:408 - Cache hit for chrome_get_current_url
2025-07-24 22:54:34.785 | INFO     | src.agents.react_agent:_react_loop:134 - Action plan: {'action': 'navigate', 'target': 'http://localhost:3000/', 'value': '', 'reason': '用户需要先访问指定的网页地址以开始报销申请流程。', 'next_steps': ['点击新建报销申请链接', '填写报销申请表单']}
2025-07-24 22:54:34.785 | INFO     | src.clients.mcp_client:navigate_to:75 - Navigating to: http://localhost:3000/
2025-07-24 22:54:34.880 | INFO     | src.clients.request_manager:log_request_success:115 - MCP request chrome_navigate completed in 94.7ms
2025-07-24 22:54:34.898 | INFO     | src.clients.request_manager:log_request_success:115 - MCP request chrome_wait_for_load completed in 14.7ms
2025-07-24 22:54:34.898 | DEBUG    | src.clients.request_manager:send_request:408 - Cache hit for chrome_get_current_url
2025-07-24 22:54:34.899 | INFO     | src.agents.react_agent:_action_navigate:201 - Navigated to: 
2025-07-24 22:54:34.900 | INFO     | src.agents.react_agent:_react_loop:123 - ReAct iteration 3
2025-07-24 22:54:34.900 | DEBUG    | src.clients.request_manager:send_request:408 - Cache hit for chrome_get_current_url
2025-07-24 22:55:54.898 | INFO     | src.agents.react_agent:_react_loop:134 - Action plan: {'action': 'navigate', 'target': 'http://localhost:3000/', 'value': '', 'reason': '用户需要先打开指定的网页地址以访问报销申请界面', 'next_steps': ['点击新建报销申请链接']}
2025-07-24 22:55:54.898 | INFO     | src.clients.mcp_client:navigate_to:75 - Navigating to: http://localhost:3000/
2025-07-24 22:55:54.998 | INFO     | src.clients.request_manager:log_request_success:115 - MCP request chrome_navigate completed in 99.6ms
2025-07-24 22:55:55.014 | INFO     | src.clients.request_manager:log_request_success:115 - MCP request chrome_wait_for_load completed in 15.0ms
2025-07-24 22:55:55.015 | DEBUG    | src.clients.request_manager:send_request:408 - Cache hit for chrome_get_current_url
2025-07-24 22:55:55.015 | INFO     | src.agents.react_agent:_action_navigate:201 - Navigated to: 
2025-07-24 22:55:55.016 | INFO     | src.agents.react_agent:_react_loop:123 - ReAct iteration 4
2025-07-24 22:55:55.016 | DEBUG    | src.clients.request_manager:send_request:408 - Cache hit for chrome_get_current_url
2025-07-24 22:56:03.543 | INFO     | src.clients.connection_manager:shutdown:214 - Shutting down connection manager...
2025-07-24 22:56:03.544 | DEBUG    | src.clients.connection_manager:_health_check_loop:170 - Health check loop cancelled
2025-07-24 22:56:03.544 | INFO     | src.clients.mcp_transport:disconnect:215 - Disconnected from MCP server
2025-07-24 22:56:03.545 | DEBUG    | src.clients.connection_manager:_disconnect_all:147 - Disconnected transport
2025-07-24 22:56:03.545 | INFO     | src.clients.connection_manager:shutdown:229 - Connection manager shutdown complete
2025-07-24 22:56:03.545 | INFO     | src.clients.mcp_client:disconnect:46 - Disconnected from Chrome MCP Server
2025-07-24 22:56:03.546 | INFO     | src.agents.react_agent:end_session:43 - ReAct Agent session ended
2025-07-24 22:56:03.546 | INFO     | __main__:stop:51 - Agent已停止
